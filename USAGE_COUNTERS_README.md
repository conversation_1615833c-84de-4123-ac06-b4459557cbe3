# Usage Counters Implementation

## Overview

This implementation adds usage counters to track command usage between two users in the Discord bot's message cogs. The system tracks how many times each command has been used between specific user pairs.

## Database Schema

A new table `command_usage_counters` has been added with the following structure:

```sql
CREATE TABLE IF NOT EXISTS command_usage_counters (
    user1_id BIGINT NOT NULL,        -- The user who initiated the command
    user2_id BIGINT NOT NULL,        -- The target user of the command  
    command_name TEXT NOT NULL,      -- The name of the command
    usage_count INTEGER NOT NULL DEFAULT 1,  -- Number of times used
    PRIMARY KEY (user1_id, user2_id, command_name)
);
```

## Modified Files

### 1. `cogs/message_cog.py`
- Added usage tracking to all commands that involve two users:
  - `molest` (slash and legacy)
  - `rape` (slash and legacy) 
  - `sex` (slash and legacy)
- Commands without target users (like `seals`, `notlikeus`, `pmo`) are not tracked

### 2. `cogs/neru_message_cog.py`
- Added usage tracking to all commands that involve two users:
  - `sex` (slash and legacy) - tracked as `neru_sex`
  - `rape` (slash command only) - tracked as `neru_rape`
  - `kiss` (slash and legacy) - tracked as `neru_kiss`
  - `hug` (slash and legacy) - tracked as `neru_hug`
- Commands without target users (like `seals`, `notlikeus`, `pmo`) are not tracked

## Implementation Details

### Helper Methods Added

Each cog now includes three helper methods:

1. **`_ensure_usage_table_exists()`**
   - Creates the usage counters table if it doesn't exist
   - Returns `True` if successful, `False` if database unavailable

2. **`_increment_usage_counter(user1_id, user2_id, command_name)`**
   - Increments the usage counter for a specific user pair and command
   - Uses PostgreSQL's `ON CONFLICT` to handle upserts efficiently
   - Logs debug information about counter increments

3. **`_get_usage_count(user1_id, user2_id, command_name)`**
   - Retrieves the current usage count for a specific combination
   - Returns 0 if no record exists or if database is unavailable

### Command Naming Convention

To distinguish between commands from different cogs, the neru_message_cog commands are prefixed with `neru_`:
- `sex` → `neru_sex`
- `rape` → `neru_rape`
- `kiss` → `neru_kiss`
- `hug` → `neru_hug`

### Database Connection Pattern

The implementation follows the existing codebase pattern:
- Uses `self.bot.pg_pool` for database connections
- Handles connection failures gracefully
- Logs errors appropriately
- Uses async context managers for proper connection handling

## Usage Examples

### Querying Usage Data

```python
# Get all usage data
SELECT user1_id, user2_id, command_name, usage_count 
FROM command_usage_counters 
ORDER BY usage_count DESC;

# Get usage between specific users
SELECT command_name, usage_count 
FROM command_usage_counters 
WHERE user1_id = $1 AND user2_id = $2;

# Get most popular commands
SELECT command_name, SUM(usage_count) as total_usage
FROM command_usage_counters 
GROUP BY command_name 
ORDER BY total_usage DESC;
```

### Testing

A test script `test_usage_counters.py` is provided to:
- Verify database connectivity
- Check if the table exists
- Display sample usage data
- Query usage between specific users

## Error Handling

The implementation includes robust error handling:
- Database connection failures are logged but don't break commands
- Table creation errors are caught and logged
- Usage tracking failures don't prevent command execution
- Graceful degradation when database is unavailable

## Performance Considerations

- Table uses a composite primary key for efficient lookups
- Upsert operations use PostgreSQL's native `ON CONFLICT` clause
- Database operations are async and non-blocking
- Connection pooling is handled by the bot's existing pool

## Future Enhancements

Potential improvements could include:
- Adding timestamps for first/last usage
- Implementing usage analytics and reporting commands
- Adding rate limiting based on usage counts
- Creating usage leaderboards
- Exporting usage statistics for analysis
