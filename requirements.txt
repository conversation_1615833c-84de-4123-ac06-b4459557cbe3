# Automatically generated by https://github.com/damnever/pigar.

aiodocker==0.24.0
aiofiles==24.1.0
aiohttp==3.11.18
aiosqlite==0.21.0
asteval==1.0.6
asyncpg==0.30.0
certifi==2025.4.26
chess==1.11.2
chromadb==1.0.7
diffusers==0.33.1
distro==1.9.0
docker==7.1.0
fastapi==0.115.9
Flask==3.1.0
google-cloud-aiplatform==1.90.0
GPUtil==1.4.0
gTTS==2.5.4
httpx==0.28.1
# huggingface-hub==0.30.2
jsonschema==4.23.0
Markdown==3.8
matplotlib==3.10.1
moviepy==2.1.2
nltk==3.9.1
numpy==1.26.4
# opencv-python==*********
pillow==10.4.0
psutil==7.0.0
pyadl==0.1
pydantic==2.11.3
pydantic-settings==2.9.1
pydub==0.25.1
python-dotenv==1.1.0
pyttsx3==2.98
redis==6.0.0
requests==2.32.3
# sentence-transformers==4.1.0
starlette==0.45.3
tavily-python==0.7.0
# torch==2.7.0
tqdm==4.67.1
# TTS==0.22.0
uvicorn==0.34.2
webrtcvad==2.0.10
WMI==1.4.9
discord-ext-voice_recv @ git+https://github.com/imayhaveborkedit/discord-ext-voice-recv@02a0782db6a1e154edbcfe252004f8fdd4bf539f
discord.py @ git+https://github.com/Slipstreamm/discord.py.git@8f03384a884fa11846ed4ead0eab4008ef1d1ca3

# WARNING(pigar): some manual fixes might be required as pigar has detected duplicate requirements for the same import name (possibly for different submodules).
# WARNING(pigar): the following duplicate requirements are for the import name: discord
# discord-ext-voice_recv==0.5.0a165
# discord.py==2.6.0a5367+g8f03384a
