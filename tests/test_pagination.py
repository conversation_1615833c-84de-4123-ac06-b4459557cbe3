import asyncio
import pytest
import discord
from discord.ext import commands

from cogs.safebooru_cog import SafebooruCog


class MockResponse:
    def __init__(self, status: int, data):
        self.status = status
        self._data = data

    async def json(self):
        return self._data

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc, tb):
        return False


class MockSession:
    def __init__(self, data):
        self.data = data
        self.closed = False

    def get(self, *args, **kwargs):
        return MockResponse(200, self.data)

    async def close(self):
        self.closed = True


@pytest.mark.asyncio
async def test_fetch_posts_logic(monkeypatch):
    intents = discord.Intents.none()
    bot = commands.Bot(command_prefix="!", intents=intents)
    cog = SafebooruCog(bot)
    mock_data = [{"id": "1", "file_url": "http://example.com/image.jpg"}]
    monkeypatch.setattr(cog, "session", MockSession(mock_data))

    results = await cog._fetch_posts_logic(
        "test", "tag", pid_override=0, limit_override=1
    )

    assert isinstance(results, list)
    assert results[0]["id"] == "1"
