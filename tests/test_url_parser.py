import re
from typing import Optional, Tu<PERSON>
from urllib.parse import urlparse
import pytest


def parse_repo_url(url: str) -> Tuple[Optional[str], Optional[str]]:
    """Parses a Git repository URL and returns platform and repo id."""
    if not re.match(r"^https?://", url):
        url = "https://" + url

    try:
        parsed = urlparse(url)
    except Exception:
        return None, None

    host = parsed.netloc.lower()
    parts = [p for p in parsed.path.strip("/").split("/") if p]

    if host.endswith("github.com"):
        if len(parts) >= 2:
            return "github", "/".join(parts[:2])
        return None, None

    if host.endswith("gitlab.com"):
        if len(parts) >= 2:
            return "gitlab", "/".join(parts)
        return None, None

    if host and len(parts) >= 2:
        return "gitea", "/".join(parts[:2])
    return None, None


@pytest.mark.parametrize(
    "url,expected",
    [
        (
            "https://github.com/Slipstreamm/discordbot",
            ("github", "Slipstreamm/discordbot"),
        ),
        (
            "http://github.com/Slipstreamm/discordbot",
            ("github", "Slipstreamm/discordbot"),
        ),
        ("github.com/Slipstreamm/discordbot", ("github", "Slipstreamm/discordbot")),
        ("www.github.com/Slipstreamm/discordbot", ("github", "Slipstreamm/discordbot")),
        ("https://github.com/Slipstreamm/git", ("github", "Slipstreamm/git")),
        ("https://gitlab.com/group/project", ("gitlab", "group/project")),
        (
            "https://gitlab.com/group/subgroup/project",
            ("gitlab", "group/subgroup/project"),
        ),
        ("https://gitea.com/org/repo", ("gitea", "org/repo")),
        ("https://mygit.example/repo1/project", ("gitea", "repo1/project")),
        ("invalid-url", (None, None)),
    ],
)
def test_parse_repo_url(url: str, expected: Tuple[Optional[str], Optional[str]]):
    assert parse_repo_url(url) == expected
