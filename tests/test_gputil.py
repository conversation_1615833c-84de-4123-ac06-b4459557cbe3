import pytest

try:
    import pyadl
except ImportError:  # pragma: no cover - dependency optional
    pyadl = None


class FakeDevice:
    def __init__(self, index: int, name: str):
        self.adapterIndex = index
        self.adapterName = name


class FakeManager:
    def getDevices(self):
        return [FakeDevice(0, "GPU0"), FakeDevice(1, "GPU1")]


@pytest.mark.skipif(pyadl is None, reason="pyadl not installed")
def test_pyadl_devices(monkeypatch):
    monkeypatch.setattr(pyadl.ADLManager, "getInstance", lambda: FakeManager())

    devices = pyadl.ADLManager.getInstance().getDevices()
    assert len(devices) == 2
    assert devices[0].adapterName == "GPU0"
