import os
import sys
import pytest

# Ensure the project root is on sys.path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cogs.git_monitor_cog import parse_repo_url


@pytest.mark.parametrize(
    "url,expected",
    [
        ("https://github.com/user/repo", ("github", "user/repo")),
        ("http://github.com/user/repo", ("github", "user/repo")),
        ("github.com/user/repo", ("github", "user/repo")),
        ("https://www.github.com/user/repo/", ("github", "user/repo")),
        ("https://github.com/user/repo.git", ("github", "user/repo")),
        ("https://github.com/user-name/re.po", ("github", "user-name/re.po")),
        ("https://gitlab.com/group/project", ("gitlab", "group/project")),
        (
            "https://gitlab.com/group/subgroup/project",
            ("gitlab", "group/subgroup/project"),
        ),
        ("gitlab.com/group/subgroup/project.git", ("gitlab", "group/subgroup/project")),
        (
            "http://www.gitlab.com/group/subgroup/project/",
            ("gitlab", "group/subgroup/project"),
        ),
        ("https://gitea.com/user/repo", ("gitea", "user/repo")),
        ("https://git.example.com/org/repo", ("gitea", "org/repo")),
    ],
)
def test_parse_repo_url_valid(url, expected):
    assert parse_repo_url(url) == expected


@pytest.mark.parametrize(
    "url",
    [
        "https://github.com/",
        "https://github.com/user",
        "https://gitlab.com/",
        "https://gitlab.com/group",
        "ftp://github.com/user/repo",
        "http:/github.com/user/repo",
        "not a url",
        "gitlab.com/group//project",
        "github.com/user/repo/extra",
    ],
)
def test_parse_repo_url_invalid(url):
    assert parse_repo_url(url) == (None, None)
