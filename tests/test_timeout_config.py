import json
from pathlib import Path


def load_timeout_config(path: Path) -> float:
    timeout_chance = 0.005
    if path.exists():
        with open(path, "r") as f:
            data = json.load(f)
            timeout_chance = data.get("timeout_chance", timeout_chance)
    return timeout_chance


def save_timeout_config(path: Path, timeout_chance: float) -> None:
    path.parent.mkdir(parents=True, exist_ok=True)
    data = {
        "timeout_chance": timeout_chance,
        "target_user_id": 748405715520978965,
        "timeout_duration": 60,
    }
    with open(path, "w") as f:
        json.dump(data, f, indent=4)


def test_timeout_config_roundtrip(tmp_path: Path):
    cfg = tmp_path / "timeout_config.json"
    save_timeout_config(cfg, 0.01)
    assert cfg.exists()
    assert load_timeout_config(cfg) == 0.01
