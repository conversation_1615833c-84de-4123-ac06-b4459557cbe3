import sys
import pytest


def test_part_constructors():
    try:
        import google.generativeai as generativeai

        sys.modules.setdefault("google.genai", generativeai)
        from gurt.api import types
    except Exception as e:  # pragma: no cover - skip if dependencies missing
        pytest.skip(f"gurt.api unavailable: {e}")

    part = types.Part(text="test")
    assert part

    uri_part = types.Part(uri="https://example.com", mime_type="text/plain")
    assert getattr(uri_part, "uri", None) == "https://example.com"
