# Project: Refactor AI Agent Tool Syntax to XML

**Date:** 2025-05-31

**Goal:** To refactor the AI agent's tool calling mechanism from a custom regex-parsed format to a standardized XML-based format. This aims to improve robustness, ease of AI generation, parsing simplicity, extensibility, and standardization.

**1. Current State:**
The AI agent in `cogs/ai_code_agent_cog.py` uses a custom, line-based syntax for tool calls, defined in `AGENT_SYSTEM_PROMPT` and parsed using regular expressions in `_parse_and_execute_tool_call`.

**2. Proposed Change: XML-Based Tool Calls**

The AI will be instructed to output *only* an XML block when calling a tool. The root element of the XML block will be the tool's name.

**2.1. New XML Tool Call Syntax Definitions:**

*   **ReadFile:**
    ```xml
    <ReadFile>
      <path>path/to/file.ext</path>
    </ReadFile>
    ```

*   **WriteFile:**
    ```xml
    <WriteFile>
      <path>path/to/file.ext</path>
      <content><![CDATA[
    Your multi-line file content here.
    Special characters like < & > are fine.
      ]]></content>
    </WriteFile>
    ```
    *(Using `CDATA` for `content` and `diff_block` is recommended to handle multi-line strings and special XML characters gracefully.)*

*   **ApplyDiff:**
    ```xml
    <ApplyDiff>
      <path>path/to/file.ext</path>
      <diff_block><![CDATA[
    --- a/original_file.py
    +++ b/modified_file.py
    @@ -1,3 +1,4 @@
     line 1
    -line 2 old
    +line 2 new
    +line 3 added
      ]]></diff_block>
    </ApplyDiff>
    ```

*   **ExecuteCommand:**
    ```xml
    <ExecuteCommand>
      <command>your shell command here</command>
    </ExecuteCommand>
    ```

*   **ListFiles:**
    ```xml
    <ListFiles>
      <path>path/to/search</path>
      <recursive>true</recursive> <!-- boolean: "true" or "false", defaults to false if tag omitted or value is not "true" -->
    </ListFiles>
    ```

*   **WebSearch:**
    ```xml
    <WebSearch>
      <query>your search query</query>
    </WebSearch>
    ```

*   **TaskComplete:**
    ```xml
    <TaskComplete>
      <message>A brief summary of what was accomplished or the final status.</message>
    </TaskComplete>
    ```

**3. Parsing Logic in `_parse_and_execute_tool_call` (Python):**

*   The method will attempt to parse the `ai_response_text` as XML using `xml.etree.ElementTree`.
*   If parsing fails (e.g., `ET.ParseError`), it's considered "NO_TOOL" or an error.
*   If successful, the root tag name will determine the `tool_name`.
*   Child elements of the root will provide the `parameters`.
    *   `.text` attribute of child elements will give their values.
    *   For `recursive` in `ListFiles`, the string "true" (case-insensitive) will be converted to a Python boolean `True`, otherwise `False`.
    *   `CDATA` sections will be handled transparently by the XML parser for `content` and `diff_block`.

**4. Changes to `AGENT_SYSTEM_PROMPT`:**
    The system prompt in `cogs/ai_code_agent_cog.py` will be updated to:
    *   Remove the old tool syntax definitions.
    *   Clearly state that tool calls must be XML, with the tool name as the root tag.
    *   Provide the exact XML examples shown above for each tool.
    *   Emphasize that the AI's response should *only* be the XML block when a tool is invoked, with no surrounding text or markdown.

**5. Implementation Steps:**

    *   **Phase 1: Update System Prompt & Define Structures**
        *   Modify `AGENT_SYSTEM_PROMPT` in `cogs/ai_code_agent_cog.py` with the new XML tool definitions and instructions.
    *   **Phase 2: Refactor Parsing Logic**
        *   Rewrite the `_parse_and_execute_tool_call` method in `cogs/ai_code_agent_cog.py` to:
            *   Import `xml.etree.ElementTree as ET`.
            *   Use a `try-except ET.ParseError` block to catch invalid XML.
            *   If XML is valid, get the root element's tag as `tool_name`.
            *   Extract parameters from child elements.
            *   Handle boolean conversion for `ListFiles/recursive`.
            *   Call the respective `_execute_tool_...` methods.
    *   **Phase 3: Testing**
        *   Thoroughly test each tool call with various inputs, including edge cases and malformed XML (to ensure robust error handling).

**6. Visual Flow (Mermaid Diagram):**

```mermaid
graph TD
    A[AI Response Text] --> B{Attempt to Parse as XML};
    B -- Valid XML --> C{Get Root Tag (Tool Name)};
    C --> D{Extract Parameters from Child Tags};
    D --> E{Switch on Tool Name};
    E -- ReadFile --> F[Call _execute_tool_read_file];
    E -- WriteFile --> G[Call _execute_tool_write_file];
    E -- ApplyDiff --> H[Call _execute_tool_apply_diff];
    E -- ExecuteCommand --> I[Call _execute_tool_execute_command];
    E -- ListFiles --> J[Call _execute_tool_list_files];
    E -- WebSearch --> K[Call _execute_tool_web_search];
    E -- TaskComplete --> L[Process TaskComplete];
    E -- Unknown Tool --> M[Handle Unknown Tool / Error];
    B -- Invalid XML / Not a Tool --> N[Return "NO_TOOL" status];
    F --> O[Format ToolResponse];
    G --> O;
    H --> O;
    I --> O;
    J --> O;
    K --> O;
    L --> P[End Interaction];
    M --> P;
    N --> P;
    O --> Q[Add to History, Continue Loop];