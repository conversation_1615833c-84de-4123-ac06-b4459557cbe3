<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Bot Configuration Dashboard</title>
    <!-- Preload fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- CSS files -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/layout.css">
</head>
<body>
    <!-- Auth Section -->
    <div id="auth-section" class="container">
        <div class="card mt-6" style="max-width: 500px; margin: 100px auto; text-align: center;">
            <h1>Custom Bot Configuration</h1>
            <p class="mb-4">Set up and manage your personalized Discord bot</p>
            <button id="login-button" class="btn btn-primary btn-lg">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>
                Login with Discord
            </button>
        </div>
    </div>

    <!-- Bot Configuration Section -->
    <div id="bot-config-section" class="container dashboard-section" style="display: none; margin-top: 80px;">
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">Custom Bot Setup</h2>
                <p class="text-muted">Configure your personalized Discord bot with your own token and settings.</p>
            </div>
        </div>
    </div>

    <!-- Main Dashboard Section -->
    <div id="dashboard-container" class="dashboard-container" style="display: none;">
        <!-- Header -->
        <header class="header">
            <h1 class="header-title">Custom Bot Configuration</h1>
            <div class="user-info">
                <div id="user-avatar" class="user-avatar">U</div>
                <span id="username" class="user-name">User</span>
                <button id="logout-button" class="btn btn-danger ml-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path><polyline points="16 17 21 12 16 7"></polyline><line x1="21" y1="12" x2="9" y2="12"></line></svg>
                    Logout
                </button>
            </div>
        </header>

        <!-- Content Container -->
        <div class="content-container" style="margin-top: 80px; padding: 20px;">

            <!-- Bot Status Section -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">Bot Status</h2>
                    <p class="text-muted">Current status of your custom Discord bot</p>
                </div>
                <div class="card-body">
                    <div id="bot-status-indicator" class="flex items-center mb-4">
                        <div id="status-dot" class="w-4 h-4 rounded-full bg-gray-400 mr-2"></div>
                        <span id="status-text">Checking status...</span>
                    </div>
                    <div id="bot-controls" class="flex gap-2">
                        <button id="start-bot-button" class="btn btn-primary" disabled>Start Bot</button>
                        <button id="stop-bot-button" class="btn btn-danger" disabled>Stop Bot</button>
                    </div>
                    <div id="bot-error" class="mt-4 text-red-500 hidden"></div>
                </div>
            </div>

            <!-- Bot Configuration Section -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">Bot Configuration</h2>
                    <p class="text-muted">Configure your custom Discord bot settings</p>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="bot-token-input">Bot Token:</label>
                        <input type="password" id="bot-token-input" class="w-full" placeholder="Enter your Discord bot token">
                        <p class="text-sm text-gray-600 mt-1">Your bot token is stored securely and never shared.</p>
                    </div>
                    <div class="form-group">
                        <label for="bot-prefix-input">Command Prefix:</label>
                        <input type="text" id="bot-prefix-input" class="w-full" placeholder="!" maxlength="5">
                        <p class="text-sm text-gray-600 mt-1">The prefix users will type before commands (e.g., !help).</p>
                    </div>
                    <div class="form-group">
                        <label for="bot-status-type-select">Status Type:</label>
                        <select id="bot-status-type-select" class="w-full">
                            <option value="playing">Playing</option>
                            <option value="listening">Listening to</option>
                            <option value="watching">Watching</option>
                            <option value="competing">Competing in</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="bot-status-text-input">Status Text:</label>
                        <input type="text" id="bot-status-text-input" class="w-full" placeholder="!help" maxlength="128">
                        <p class="text-sm text-gray-600 mt-1">The text that will appear in your bot's status.</p>
                    </div>
                    <div class="btn-group">
                        <button id="save-bot-config-button" class="btn btn-primary">Save Configuration</button>
                    </div>
                    <p id="bot-config-feedback" class="mt-2"></p>
                </div>
            </div>

            <!-- Bot Setup Guide Section -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">How to Create Your Discord Bot</h2>
                    <p class="text-muted">Follow these steps to set up your custom Discord bot</p>
                </div>
                <div class="card-body">
                    <ol class="list-decimal pl-5 space-y-4">
                        <li>
                            <strong>Create a Discord Application:</strong>
                            <p>Go to the <a href="https://discord.com/developers/applications" target="_blank" class="text-blue-500 hover:underline">Discord Developer Portal</a> and click "New Application".</p>
                        </li>
                        <li>
                            <strong>Set Up Your Bot:</strong>
                            <p>In your application, go to the "Bot" tab and click "Add Bot".</p>
                        </li>
                        <li>
                            <strong>Customize Your Bot:</strong>
                            <p>Upload a profile picture and set a username for your bot.</p>
                        </li>
                        <li>
                            <strong>Get Your Bot Token:</strong>
                            <p>Click "Reset Token" to generate a new token, then copy it.</p>
                            <p class="text-red-500">IMPORTANT: Never share your bot token with anyone!</p>
                        </li>
                        <li>
                            <strong>Set Bot Permissions:</strong>
                            <p>In the "Bot" tab, under "Privileged Gateway Intents", enable:</p>
                            <ul class="list-disc pl-5">
                                <li>Presence Intent</li>
                                <li>Server Members Intent</li>
                                <li>Message Content Intent</li>
                            </ul>
                        </li>
                        <li>
                            <strong>Invite Your Bot:</strong>
                            <p>Go to the "OAuth2" tab, then "URL Generator". Select the following scopes:</p>
                            <ul class="list-disc pl-5">
                                <li>bot</li>
                                <li>applications.commands</li>
                            </ul>
                            <p>Then select the following bot permissions:</p>
                            <ul class="list-disc pl-5">
                                <li>Send Messages</li>
                                <li>Embed Links</li>
                                <li>Attach Files</li>
                                <li>Read Message History</li>
                                <li>Use Slash Commands</li>
                                <li>Add Reactions</li>
                            </ul>
                            <p>Copy the generated URL and open it in your browser to invite the bot to your server.</p>
                        </li>
                        <li>
                            <strong>Configure Your Bot:</strong>
                            <p>Paste your bot token in the configuration form above and save it.</p>
                        </li>
                        <li>
                            <strong>Start Your Bot:</strong>
                            <p>Click the "Start Bot" button to bring your bot online!</p>
                        </li>
                    </ol>
                </div>
            </div>

        </div>
    </div>
    </div>

    <!-- JavaScript files -->
    <script src="js/utils.js"></script>
    <script src="js/custom-bot.js"></script>
</body>
</html>
