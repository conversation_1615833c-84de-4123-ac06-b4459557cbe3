/* Main CSS file for the Discord Bot Dashboard */

:root {
  /* Color variables */
  --primary-color: #5865F2; /* Discord blue */
  --primary-hover: #4752C4;
  --secondary-color: #2D3748;
  --accent-color: #7289DA;
  --success-color: #48BB78;
  --warning-color: #F6AD55;
  --danger-color: #F56565;
  --light-bg: #F7FAFC;
  --dark-bg: #1A202C;
  --card-bg: #FFFFFF;
  --text-primary: #2D3748;
  --text-secondary: #718096;
  --border-color: #E2E8F0;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;

  /* Spacing variables */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
}

/* Base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--light-bg);
  color: var(--text-primary);
  line-height: 1.5;
  min-height: 100vh;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.25;
  margin-bottom: var(--spacing-4);
  color: var(--secondary-color);
}

h1 {
  font-size: 2rem;
}

h2 {
  font-size: 1.5rem;
}

h3 {
  font-size: 1.25rem;
}

h4 {
  font-size: 1rem;
}

a {
  color: var(--primary-color);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* Layout */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.dashboard-container {
  display: flex;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  padding: var(--spacing-6);
  margin-left: 250px; /* Width of the sidebar */
}

/* Header */
.header {
  background-color: var(--card-bg);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-4) var(--spacing-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  top: 0;
  right: 0;
  left: 250px; /* Width of the sidebar */
  height: 64px;
  z-index: 10;
  box-shadow: var(--shadow-sm);
}

.header-title {
  font-size: 1.25rem;
  margin: 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
}

.user-name {
  font-weight: 500;
}

/* Sidebar */
.sidebar {
  width: 250px;
  background-color: var(--secondary-color);
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  overflow-y: auto;
  z-index: 20;
}

.sidebar-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  text-decoration: none;
}

.sidebar-nav {
  padding: var(--spacing-4) 0;
}

.nav-item {
  padding: var(--spacing-3) var(--spacing-6);
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: background-color 0.2s;
}

.nav-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  text-decoration: none;
}

.nav-item.active {
  background-color: var(--primary-color);
  color: white;
}

.nav-icon {
  width: 20px;
  height: 20px;
}

.sidebar-footer {
  padding: var(--spacing-4) var(--spacing-6);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  position: absolute;
  bottom: 0;
  width: 100%;
}

/* Cards */
.card {
  background-color: var(--card-bg);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-6);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
}

.card-title {
  font-size: 1.25rem;
  margin: 0;
}

/* Forms */
.form-group {
  margin-bottom: var(--spacing-4);
}

label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: 500;
}

input[type="text"],
input[type="number"],
input[type="password"],
input[type="email"],
input[type="search"],
select,
textarea {
  width: 100%;
  padding: var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: white;
  font-size: 1rem;
  transition: border-color 0.2s;
}

input:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(88, 101, 242, 0.2);
}

textarea {
  resize: vertical;
  min-height: 100px;
}

input[type="checkbox"],
input[type="radio"] {
  margin-right: var(--spacing-2);
}

.checkbox-label,
.radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2) var(--spacing-4);
  border: none;
  border-radius: var(--radius-md);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
}

.btn:hover {
  transform: translateY(-1px);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: #3a4a63;
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover {
  background-color: #3da066;
}

.btn-warning {
  background-color: var(--warning-color);
  color: white;
}

.btn-warning:hover {
  background-color: #e09c49;
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background-color: #e04c4c;
}

.btn-sm {
  font-size: 0.875rem;
  padding: var(--spacing-1) var(--spacing-3);
}

.btn-lg {
  font-size: 1.125rem;
  padding: var(--spacing-3) var(--spacing-6);
}

.btn-icon {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
}

.btn-group {
  display: flex;
  gap: var(--spacing-3);
}

/* Utilities */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mt-1 { margin-top: var(--spacing-1); }
.mt-2 { margin-top: var(--spacing-2); }
.mt-4 { margin-top: var(--spacing-4); }
.mt-6 { margin-top: var(--spacing-6); }

.mb-1 { margin-bottom: var(--spacing-1); }
.mb-2 { margin-bottom: var(--spacing-2); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-6 { margin-bottom: var(--spacing-6); }

.ml-1 { margin-left: var(--spacing-1); }
.ml-2 { margin-left: var(--spacing-2); }
.ml-4 { margin-left: var(--spacing-4); }
.ml-6 { margin-left: var(--spacing-6); }

.mr-1 { margin-right: var(--spacing-1); }
.mr-2 { margin-right: var(--spacing-2); }
.mr-4 { margin-right: var(--spacing-4); }
.mr-6 { margin-right: var(--spacing-6); }

.p-1 { padding: var(--spacing-1); }
.p-2 { padding: var(--spacing-2); }
.p-4 { padding: var(--spacing-4); }
.p-6 { padding: var(--spacing-6); }

.hidden {
  display: none !important;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.gap-2 {
  gap: var(--spacing-2);
}

.gap-4 {
  gap: var(--spacing-4);
}

.w-full {
  width: 100%;
}

.text-sm {
  font-size: 0.875rem;
}

.text-lg {
  font-size: 1.125rem;
}

.font-bold {
  font-weight: 700;
}

.text-gray {
  color: var(--text-secondary);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-danger {
  color: var(--danger-color);
}

/* Custom Bot Configuration Dashboard Styles */

/* Header for the streamlined dashboard */
.header {
  background-color: var(--card-bg);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-4) var(--spacing-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: var(--shadow-sm);
}

.header-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.125rem;
}

.user-name {
  font-weight: 500;
  color: var(--text-primary);
}

/* Bot status indicator */
.w-4 {
  width: 1rem;
}

.h-4 {
  height: 1rem;
}

.rounded-full {
  border-radius: 50%;
}

.bg-gray-400 {
  background-color: #9CA3AF;
}

.bg-green-500 {
  background-color: var(--success-color);
}

.bg-red-500 {
  background-color: var(--danger-color);
}

.bg-yellow-500 {
  background-color: var(--warning-color);
}

/* Text colors */
.text-red-500 {
  color: var(--danger-color);
}

.text-green-500 {
  color: var(--success-color);
}

.text-gray-600 {
  color: var(--text-secondary);
}

.text-blue-500 {
  color: var(--primary-color);
}

.hover\:underline:hover {
  text-decoration: underline;
}

/* List styles for setup guide */
.list-decimal {
  list-style-type: decimal;
}

.list-disc {
  list-style-type: disc;
}

.pl-5 {
  padding-left: 1.25rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .header {
    padding: var(--spacing-3) var(--spacing-4);
  }

  .header-title {
    font-size: 1.25rem;
  }

  .user-info {
    gap: var(--spacing-2);
  }

  .user-avatar {
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }

  .content-container {
    padding: var(--spacing-4) !important;
  }

  .btn-group {
    flex-direction: column;
  }
}
