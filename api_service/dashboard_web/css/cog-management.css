/* Cog Management Styles */

.cog-card, .command-card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    transition: all 0.2s ease;
}

.cog-card:hover, .command-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cog-badge {
    background-color: var(--primary-color-light);
    color: var(--primary-color-dark);
}

.command-count {
    background-color: var(--secondary-color-light);
    color: var(--secondary-color-dark);
}

.cogs-list-container, .commands-list-container {
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
}

/* Loading container */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.loading-spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid var(--primary-color);
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Checkbox styling */
input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--border-color);
    border-radius: 0.25rem;
    background-color: var(--card-bg);
    display: inline-block;
    position: relative;
    margin-right: 0.5rem;
    vertical-align: middle;
    cursor: pointer;
}

input[type="checkbox"]:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

input[type="checkbox"]:checked::after {
    content: "";
    position: absolute;
    left: 0.3rem;
    top: 0.1rem;
    width: 0.5rem;
    height: 0.8rem;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

input[type="checkbox"]:disabled {
    background-color: var(--disabled-bg);
    border-color: var(--disabled-border);
    cursor: not-allowed;
}

input[type="checkbox"]:disabled:checked {
    background-color: var(--disabled-checked-bg);
}

input[type="checkbox"]:disabled:checked::after {
    border-color: var(--disabled-checked-color);
}

/* Grid layout for larger screens */
@media (min-width: 768px) {
    .grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

/* Feedback messages */
.text-green-600 {
    color: #059669;
}

.text-red-600 {
    color: #dc2626;
}
