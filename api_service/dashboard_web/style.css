/* This file is kept for backward compatibility */
/* It imports the new modular CSS files */

@import url('css/main.css');
@import url('css/components.css');
@import url('css/layout.css');

/* Legacy styles below for backward compatibility */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    margin: 0;
    background-color: var(--light-bg);
    color: var(--text-primary);
}

h1, h2, h3, h4 {
    color: var(--secondary-color);
}

#dashboard-section {
    background-color: #fff;
    padding: 1.5em;
    border-radius: 8px;
    margin-top: 1em;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dashboard-section {
    margin-top: 1em;
}

.settings-card {
    background-color: #fff;
    padding: 1.5em;
    border-radius: 8px;
    margin-top: 1em;
    margin-bottom: 1.5em;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #eee;
}

.form-group {
    margin-bottom: 1em;
}

label {
    display: block;
    margin-bottom: 0.5em;
    font-weight: bold;
}

input[type="text"],
input[type="number"],
select,
textarea {
    width: 95%;
    padding: 8px;
    margin-bottom: 0.5em;
    border: 1px solid #ccc;
    border-radius: 4px;
}

input[type="checkbox"] {
    margin-right: 8px;
}

input[type="range"] {
    width: 95%;
}

textarea {
    resize: vertical;
}

.button-group {
    margin-top: 1em;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

button {
    padding: 10px 15px;
    background-color: #5865F2; /* Discord blue */
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

button:hover {
    background-color: #4752C4;
}

#logout-button {
    background-color: #dc3545; /* Red */
}
#logout-button:hover {
    background-color: #c82333;
}

button[id^="disable-"],
button[id^="reset-"],
button[id^="clear-"] {
    background-color: #ffc107; /* Yellow/Orange */
    color: #333;
}
button[id^="disable-"]:hover,
button[id^="reset-"]:hover,
button[id^="clear-"]:hover {
    background-color: #e0a800;
}

hr {
    border: 0;
    height: 1px;
    background: #ddd;
    margin: 2em 0;
}

/* Navigation */
.dashboard-nav {
    display: flex;
    margin-top: 1em;
    border-bottom: 1px solid #ddd;
}

.nav-button {
    background: none;
    border: none;
    padding: 10px 15px;
    margin: 0;
    color: #666;
    cursor: pointer;
    border-bottom: 3px solid transparent;
}

.nav-button:hover {
    background-color: #f0f0f0;
    color: #333;
}

.nav-button.active {
    color: #5865F2;
    border-bottom: 3px solid #5865F2;
    font-weight: bold;
}

/* Channel Select Container */
.channel-select-container {
    display: flex;
    gap: 8px;
}

.channel-dropdown {
    flex: 1;
}

/* Cogs List */
.cogs-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #eee;
    padding: 10px;
    border-radius: 4px;
}

#cogs-list div {
    margin-bottom: 0.5em;
}

#cogs-list label {
    display: inline-block;
    margin-left: 5px;
    font-weight: normal;
}

/* Command Permissions Section */
#current-perms {
    margin-top: 1em;
    padding: 0.5em;
    border: 1px solid #eee;
    max-height: 200px;
    overflow-y: auto;
}
#current-perms div {
    margin-bottom: 0.3em;
    font-size: 0.9em;
}
#current-perms span {
    font-weight: bold;
}
#add-perm-button {
    background-color: #28a745; /* Green */
}
#add-perm-button:hover {
    background-color: #218838;
}
#remove-perm-button {
    background-color: #dc3545; /* Red */
}
#remove-perm-button:hover {
    background-color: #c82333;
}

/* Conversations Section */
.conversations-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1em;
}

.search-container {
    flex: 1;
    margin-right: 1em;
}

.conversations-list-container {
    border: 1px solid #ddd;
    border-radius: 8px;
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 1em;
}

#conversations-list {
    padding: 0;
}

.conversation-item {
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
}

.conversation-item:hover {
    background-color: #f5f5f5;
}

.conversation-item.active {
    background-color: #e6f7ff;
    border-left: 3px solid #5865F2;
}

.conversation-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.conversation-title {
    font-weight: bold;
    margin: 0;
}

.conversation-date {
    font-size: 0.8em;
    color: #666;
}

.conversation-preview {
    font-size: 0.9em;
    color: #666;
    margin-top: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.no-conversations {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
}

#conversation-detail {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
}

.conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1em;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.conversation-actions {
    display: flex;
    gap: 8px;
}

.conversation-messages {
    max-height: 500px;
    overflow-y: auto;
    padding: 10px;
}

.message {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 8px;
}

.user-message {
    background-color: #e6f7ff;
    margin-left: 20px;
}

.ai-message {
    background-color: #f0f0f0;
    margin-right: 20px;
}

.message-header {
    font-weight: bold;
    margin-bottom: 5px;
}

.message-content {
    white-space: pre-wrap;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: #fff;
    margin: 15% auto;
    padding: 20px;
    border-radius: 8px;
    width: 50%;
    max-width: 500px;
    position: relative;
}

.close-modal {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    cursor: pointer;
}

/* Feedback messages */
p[id$="-feedback"] {
    font-style: italic;
    color: green;
    margin-top: 5px;
    min-height: 1em; /* Reserve space */
}

p[id$="-feedback"].error {
    color: red;
}
