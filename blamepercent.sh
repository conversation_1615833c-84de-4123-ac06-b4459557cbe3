#!/bin/bash

# --- Input Parameter ---
# Check if file path is provided BEFORE enabling set -u
# This ensures a user-friendly message for missing arguments.
if [ -z "$1" ]; then
    echo "Usage: $0 <FilePath>"
    exit 1
fi

FILE_PATH="$1"

# --- Error Handling ---
# Exit immediately if a command exits with a non-zero status.
set -e
# Treat unset variables as an error.
# Now that $1 is safely assigned (or the script exited), set -u is safe here.
set -u

# Check if the file exists
if [ ! -f "$FILE_PATH" ]; then
    echo "Error: File '$FILE_PATH' does not exist."
    exit 1
fi

# Check if git is installed and available
if ! command -v git &> /dev/null; then
    echo "Error: 'git' command not found. Please install Git."
    exit 1
fi

# --- Main Logic ---

# Run git blame on the file, extract author names
# We use --line-porcelain for detailed output, then grep for 'author ' lines,
# and finally use sed to extract just the author name.
# Sort and uniq -c will give us counts for each author.
blame_output=$(git blame --line-porcelain "$FILE_PATH" | \
                grep '^author ' | \
                sed 's/^author //g')

if [ -z "$blame_output" ]; then
    echo "Error: No git blame info found for '$FILE_PATH'. Make sure this file is tracked by git."
    exit 1
fi

# Initialize associative array for author counts (Bash 4.0+ required)
declare -A author_counts

total_lines=0

# Clean-AuthorName equivalent: remove control characters and non-printable characters.
# This sed expression keeps printable ASCII (0x20-0x7E) and multi-byte UTF-8 characters.
# It removes characters like carriage returns, newlines, and other control codes.
clean_author_name() {
    local name="$1"
    # Remove characters not in printable ASCII range or common extended characters.
    # This might need refinement depending on the exact non-printable characters encountered.
    # For simplicity, we'll focus on removing common control characters.
    echo "$name" | tr -d '\0-\031\177' # Remove ASCII control characters and DEL
}

# Count lines per author
while IFS= read -r author; do
    # Call the cleaning function
    cleaned_author=$(clean_author_name "$author")

    # DEBUGGING OUTPUT
    echo "[DEBUG] Author='$author' Cleaned='$cleaned_author'" >&2

    # Increment count for the cleaned author
    if [[ -n "${author_counts[$cleaned_author]:-}" ]]; then
        author_counts["$cleaned_author"]=$((author_counts["$cleaned_author"] + 1))
    else
        author_counts["$cleaned_author"]=1
    fi
    total_lines=$((total_lines + 1))
done <<< "$blame_output"

# Calculate and display percentages
echo "Code contribution percentages for file: $FILE_PATH"

if [ "$total_lines" -eq 0 ]; then
    echo "No lines found to analyze."
    exit 0
fi

# Iterate over the keys (authors) in the associative array
for author in "${!author_counts[@]}"; do
    count="${author_counts[$author]}"
    # Use awk for floating-point arithmetic for percentage calculation
    percentage=$(awk "BEGIN {printf \"%.2f\", (($count / $total_lines) * 100)}")
    echo "$author : $percentage %"
done
