body {
    font-family: sans-serif;
    line-height: 1.6;
    margin: 20px;
    background-color: #f4f4f4;
    color: #333;
}

h1, h2 {
    color: #333;
    border-bottom: 1px solid #ccc;
    padding-bottom: 5px;
}

#stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.stats-section {
    background-color: #fff;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-section h2 {
    margin-top: 0;
    font-size: 1.2em;
}

.stat-item {
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px dotted #eee;
}

.stat-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.stat-label {
    font-weight: bold;
    display: block;
    margin-bottom: 3px;
}

.stat-value {
    font-family: monospace;
    word-wrap: break-word;
}

.stat-value code {
    background-color: #eee;
    padding: 2px 4px;
    border-radius: 3px;
}

.stat-list {
    list-style: none;
    padding-left: 0;
}

.stat-list li {
    margin-bottom: 5px;
}

.error {
    color: red;
    font-weight: bold;
}

#last-updated {
    font-style: italic;
    color: #555;
}
