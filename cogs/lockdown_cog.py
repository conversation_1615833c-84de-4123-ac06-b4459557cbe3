import discord
from discord.ext import commands
from discord import app_commands
import asyncio


class LockdownCog(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    lockdown = app_commands.Group(name="lockdown", description="Lockdown commands")

    @lockdown.command(name="channel")
    @app_commands.describe(
        channel="The channel to lock down", time="Duration of lockdown in seconds"
    )
    @app_commands.checks.has_permissions(manage_channels=True)
    async def channel_lockdown(
        self,
        interaction: discord.Interaction,
        channel: discord.TextChannel = None,
        time: int = None,
    ):
        """Locks down a channel."""
        channel = channel or interaction.channel
        overwrite = channel.overwrites_for(interaction.guild.default_role)
        if overwrite.send_messages is False:
            await interaction.response.send_message(
                "Channel is already locked down.", ephemeral=True
            )
            return

        overwrite.send_messages = False
        await channel.set_permissions(
            interaction.guild.default_role, overwrite=overwrite
        )
        await interaction.response.send_message(
            f"Channel {channel.mention} locked down."
        )

        if time:
            await asyncio.sleep(time)

            overwrite.send_messages = None
            await channel.set_permissions(
                interaction.guild.default_role, overwrite=overwrite
            )
            await interaction.followup.send(
                f"Channel {channel.mention} lockdown lifted."
            )

    @lockdown.command(name="server")
    @app_commands.describe(time="Duration of server lockdown in seconds")
    @app_commands.checks.has_permissions(administrator=True)
    async def server_lockdown(self, interaction: discord.Interaction, time: int = None):
        """Locks down the entire server."""
        await interaction.response.defer()  # Defer the response as this might take time

        for channel in interaction.guild.text_channels:
            overwrite = channel.overwrites_for(interaction.guild.default_role)
            if overwrite.send_messages is False:
                continue

            overwrite.send_messages = False
            await channel.set_permissions(
                interaction.guild.default_role, overwrite=overwrite
            )

        await interaction.followup.send("Server locked down.")

        if time:
            await asyncio.sleep(time)

            for channel in interaction.guild.text_channels:
                overwrite = channel.overwrites_for(interaction.guild.default_role)
                overwrite.send_messages = None
                await channel.set_permissions(
                    interaction.guild.default_role, overwrite=overwrite
                )

            await interaction.followup.send("Server lockdown lifted.")

    @lockdown.command(name="remove_channel")
    @app_commands.describe(channel="The channel to unlock")
    @app_commands.checks.has_permissions(manage_channels=True)
    async def channel_remove(
        self, interaction: discord.Interaction, channel: discord.TextChannel = None
    ):
        """Removes lockdown from a channel."""
        channel = channel or interaction.channel
        overwrite = channel.overwrites_for(interaction.guild.default_role)
        if overwrite.send_messages is None or overwrite.send_messages is True:
            await interaction.response.send_message(
                "Channel is not locked down.", ephemeral=True
            )
            return

        overwrite.send_messages = None
        await channel.set_permissions(
            interaction.guild.default_role, overwrite=overwrite
        )
        await interaction.response.send_message(f"Channel {channel.mention} unlocked.")

    @lockdown.command(name="remove_server")
    @app_commands.checks.has_permissions(administrator=True)
    async def server_remove(self, interaction: discord.Interaction):
        """Removes lockdown from the entire server."""
        await interaction.response.defer()

        for channel in interaction.guild.text_channels:
            overwrite = channel.overwrites_for(interaction.guild.default_role)
            overwrite.send_messages = None
            await channel.set_permissions(
                interaction.guild.default_role, overwrite=overwrite
            )

        await interaction.followup.send("Server unlocked.")


async def setup(bot: commands.Bot):
    await bot.add_cog(LockdownCog(bot))
