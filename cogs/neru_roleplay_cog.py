import discord
from discord.ext import commands
from discord import app_commands


class RoleplayCog(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        print("RoleplayCog initialized!")

    async def _backshots_logic(self, sender_mention, recipient_mention, reverse=False):
        """Core logic for the backshots command."""
        if reverse:
            sender_mention, recipient_mention = recipient_mention, sender_mention
        # Format the message with sender and recipient mentions
        message = f"*{sender_mention} giving {recipient_mention} BACKSHOTS*\n{recipient_mention}: w-wait.. not in front of people-!\n{sender_mention}: \"shhh, it's okay, let them watch... 𝐥𝐞𝐭 𝐭𝐡𝐞𝐦 𝐤𝐧𝐨𝐰 𝐲𝐨𝐮'𝐫𝐞 𝐦𝐢𝐧𝐞...\""
        return message

    # --- Prefix Command ---
    @commands.command(name="backshots")
    async def backshots(
        self, ctx: commands.Context, recipient: discord.User, reverse: bool = False
    ):
        """Send a roleplay message about giving backshots to a mentioned user."""
        sender_mention = ctx.author.mention
        response = await self._backshots_logic(
            sender_mention, recipient.mention, reverse=reverse
        )
        await ctx.send(response)

    # --- Slash Command ---
    @app_commands.command(
        name="backshots",
        description="Send a roleplay message about giving backshots to a mentioned user",
    )
    @app_commands.describe(
        recipient="The user receiving backshots",
        reverse="Reverse the roles of the sender and recipient",
    )
    async def backshots_slash(
        self,
        interaction: discord.Interaction,
        recipient: discord.User,
        reverse: bool = False,
    ):
        """Slash command version of backshots."""
        sender_mention = interaction.user.mention
        response = await self._backshots_logic(
            sender_mention, recipient.mention, reverse=reverse
        )
        await interaction.response.send_message(response)


async def setup(bot: commands.Bot):
    await bot.add_cog(RoleplayCog(bot))
