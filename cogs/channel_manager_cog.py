import discord
from discord.ext import commands
from discord import app_commands
import logging
import shlex
from typing import Optional, List, Dict, Any, Tuple, Union
from types import SimpleNamespace

# Set up logging
logger = logging.getLogger(__name__)


# --- New Helper Function for Previewing ---

def _get_preview_structure(
    guild: discord.Guild, staged_changes: Dict[str, List[Any]]
) -> List[Dict]:
    """Builds a simulated, sorted channel structure based on staged changes."""
    preview_items = {}
    # 1. Create a mutable copy of all items from the guild
    for item in sorted(guild.channels, key=lambda c: c.position):
        is_cat = isinstance(item, discord.CategoryChannel)
        is_voice = isinstance(item, discord.VoiceChannel)
        is_text = isinstance(item, discord.TextChannel)

        preview_items[item.id] = {
            "id": item.id,
            "name": item.name,
            "type": "category" if is_cat else ("voice" if is_voice else "text"),
            "position": item.position,
            "category_id": getattr(item, "category_id", None),
            "overwrites": getattr(item, "overwrites", {}).copy(),
            "mention": item.mention,
            "is_new": False,
            "original": item,
            "topic": getattr(item, "topic", None) if is_text else None,
            "nsfw": getattr(item, "nsfw", False) if is_text else False,
            "slowmode_delay": getattr(item, "slowmode_delay", 0) if is_text else 0,
            "bitrate": getattr(item, "bitrate", 64000) if is_voice else 0,
            "user_limit": getattr(item, "user_limit", 0) if is_voice else 0,
        }

    # 2. Mark deletions
    deleted_ids = {d["id"] for d in staged_changes.get("deletions", [])}

    # 3. Apply creations
    new_item_map = {}  # Maps temp ID to creation dict
    for i, creation in enumerate(staged_changes.get("creations", [])):
        temp_id = f"staged_{i}_{creation['name']}"
        item_type = (
            "category"
            if creation["type"] == "category"
            else ("voice" if creation.get("is_voice") else "text")
        )
        emoji = "📁" if item_type == "category" else ("🔊" if item_type == "voice" else "#")

        preview_items[temp_id] = {
            "id": temp_id,
            "name": creation["name"],
            "type": item_type,
            "position": 9000 + i,
            "category_id": None,
            "overwrites": {},
            "mention": f"{emoji} **{creation['name']}** (New)",
            "is_new": True,
            "original": None,
            "topic": creation.get("topic"),
            "nsfw": creation.get("nsfw", False),
            "slowmode_delay": creation.get("slowmode_delay", 0),
            "bitrate": creation.get("bitrate", 64000),
            "user_limit": creation.get("user_limit", 0),
        }
        # Map the original creation name to the temp ID for moves
        if creation["type"] == "category":
            new_item_map[creation["name"]] = temp_id

    # 4. Apply edits
    for edit in staged_changes.get("edits", []):
        if edit["id"] in preview_items:
            preview_items[edit["id"]].update(edit)

    # 5. Apply moves
    for move in staged_changes.get("moves", []):
        if move["id"] in preview_items:
            new_cat_id = move.get("new_category_id")
            if move.get("new_category_name"):
                new_cat_id = new_item_map.get(move["new_category_name"])
            preview_items[move["id"]]["category_id"] = new_cat_id

    # 6. Apply permission changes
    for perm in staged_changes.get("permissions", []):
        channel_id = perm["channel_id"]
        if channel_id in preview_items:
            target_id = perm["target_id"]
            overwrite = discord.PermissionOverwrite(**perm["overwrite"])
            target_obj = guild.get_role(target_id) or guild.get_member(target_id)
            if target_obj:
                preview_items[channel_id]["overwrites"][target_obj] = overwrite

    # Filter out deleted items
    active_items = {
        id: item for id, item in preview_items.items() if id not in deleted_ids
    }

    # 7. Build hierarchy
    structure = {
        None: {"id": None, "name": "(No Category)", "channels": [], "position": -1}
    }
    for item_id, item in active_items.items():
        if item["type"] == "category":
            structure[item_id] = {**item, "channels": []}

    for item_id, item in active_items.items():
        if item["type"] != "category":
            cat_id = item["category_id"]
            if cat_id in structure:
                structure[cat_id]["channels"].append(item)
            else:
                structure[None]["channels"].append(item)

    # 8. Sort categories and channels by position
    sorted_structure = sorted(structure.values(), key=lambda x: x["position"])
    for category in sorted_structure:
        category["channels"].sort(key=lambda x: x["position"])

    # 9. Apply relative position changes
    for pos_change in staged_changes.get("positions", []):
        item_id = pos_change["id"]
        direction = pos_change["direction"]
        is_category_move = active_items.get(item_id, {}).get("type") == "category"

        target_list = (
            [c for c in sorted_structure if c["id"] is not None]
            if is_category_move
            else None
        )
        if not is_category_move:
            cat_id = active_items.get(item_id, {}).get("category_id")
            # Find the category in the sorted list
            cat_container = next((c for c in sorted_structure if c["id"] == cat_id), None)
            if cat_container:
                target_list = cat_container["channels"]

        if not target_list:
            continue

        try:
            current_index = next(
                i for i, x in enumerate(target_list) if x["id"] == item_id
            )
        except StopIteration:
            continue

        new_index = current_index - 1 if direction == "up" else current_index + 1
        if 0 <= new_index < len(target_list):
            item_to_move = target_list.pop(current_index)
            target_list.insert(new_index, item_to_move)

    return sorted_structure


# --- Modals ---


class CategoryEditModal(discord.ui.Modal, title="Create/Edit Category"):
    def __init__(
        self, view: "ChannelManagerView", category: Optional[discord.CategoryChannel] = None
    ):
        super().__init__()
        self.view = view
        self.category = category
        self.name_input = discord.ui.TextInput(
            label="Category Name",
            placeholder="Enter the category name",
            default=category.name if category else "",
            required=True,
        )
        self.add_item(self.name_input)

    async def on_submit(self, interaction: discord.Interaction):
        await self.view.stage_item_edit(interaction, self)


class ChannelEditModal(discord.ui.Modal, title="Create/Edit Channel"):
    def __init__(
        self,
        view: "ChannelManagerView",
        channel: Optional[discord.abc.GuildChannel] = None,
        is_voice=False,
    ):
        super().__init__()
        self.view = view
        self.channel = channel
        self.is_voice = is_voice
        self.name_input = discord.ui.TextInput(
            label="Channel Name",
            placeholder="Enter the channel name",
            default=channel.name if channel else "",
            required=True,
        )
        self.add_item(self.name_input)

        if not is_voice:
            self.topic_input = discord.ui.TextInput(
                label="Topic",
                placeholder="Enter the channel topic",
                default=channel.topic
                if channel and isinstance(channel, discord.TextChannel)
                else "",
                required=False,
                style=discord.TextStyle.paragraph,
            )
            self.add_item(self.topic_input)

            self.nsfw_input = discord.ui.TextInput(
                label="NSFW (true/false)",
                placeholder="Enter 'true' or 'false'",
                default=str(channel.nsfw).lower()
                if channel and hasattr(channel, "nsfw")
                else "false",
                required=False,
                max_length=5,
                style=discord.TextStyle.short,
            )
            self.add_item(self.nsfw_input)

            self.slowmode_input = discord.ui.TextInput(
                label="Slowmode (seconds)",
                placeholder="Enter delay in seconds (0 to disable)",
                default=str(channel.slowmode_delay)
                if channel and hasattr(channel, "slowmode_delay")
                else "0",
                required=False,
                max_length=6,
            )
            self.add_item(self.slowmode_input)

        if is_voice:
            self.bitrate_input = discord.ui.TextInput(
                label="Bitrate (kbps)",
                placeholder="e.g., 64 (8-384)",
                default=str(int(channel.bitrate / 1000))
                if channel and hasattr(channel, "bitrate")
                else "64",
                required=False,
                max_length=3,
            )
            self.add_item(self.bitrate_input)

            self.user_limit_input = discord.ui.TextInput(
                label="User Limit",
                placeholder="Enter max users (0 for unlimited)",
                default=str(channel.user_limit)
                if channel and hasattr(channel, "user_limit")
                else "0",
                required=False,
                max_length=3,
            )
            self.add_item(self.user_limit_input)

    async def on_submit(self, interaction: discord.Interaction):
        await self.view.stage_item_edit(interaction, self)


# Helper function to generate the embed
def _generate_channel_tree_embed(
    guild: discord.Guild, staged_changes: Dict[str, List[Any]]
) -> discord.Embed:
    """Generates an embed showing the current and staged channel layout."""
    embed = discord.Embed(
        title=f"Channel Layout Preview for {guild.name}", color=discord.Color.blue()
    )

    preview_structure = _get_preview_structure(guild, staged_changes)
    description = []

    if not preview_structure:
        description.append("No channels or categories found.")
    else:
        for category_info in preview_structure:
            cat_name = category_info["name"]
            cat_id = category_info["id"]

            # Skip the "(No Category)" header if it has no channels
            if cat_id is None and not category_info["channels"]:
                continue

            if category_info.get("is_new"):
                cat_name = f"**{cat_name}** (New)"

            description.append(f"📁 {cat_name}")

            if not category_info["channels"]:
                description.append("  - *(Empty)*")
            else:
                for channel_info in category_info["channels"]:
                    ch_name = channel_info["name"]
                    if channel_info.get("is_new"):
                        ch_name = f"**{ch_name}** (New)"

                    ch_type = "🔊" if channel_info["type"] == "voice" else "#"
                    description.append(f"  {ch_type} {ch_name}")

    embed.description = "\n".join(description)
    embed.set_footer(text="Use the controls below to manage channels.")
    return embed


class ConfirmDeleteView(discord.ui.View):
    def __init__(self, view: "ChannelManagerView"):
        super().__init__(timeout=60)
        self.view = view
        self.value = None

    @discord.ui.button(label="Confirm", style=discord.ButtonStyle.danger)
    async def confirm(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.value = True
        self.stop()
        await self.view.stage_item_delete(interaction)

    @discord.ui.button(label="Cancel", style=discord.ButtonStyle.secondary)
    async def cancel(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.value = False
        self.stop()
        await interaction.response.edit_message(content="Deletion cancelled.", view=None)


class SelectTargetModal(discord.ui.Modal, title="Select Target for Permissions"):
    def __init__(self, view: "PermissionsEditorView"):
        super().__init__()
        self.view = view
        self.target_input = discord.ui.TextInput(
            label="Role or User ID",
            placeholder="Enter the ID of the role or user",
            required=True,
        )
        self.add_item(self.target_input)

    async def on_submit(self, interaction: discord.Interaction):
        target_id_str = self.target_input.value.strip()
        if not target_id_str.isdigit():
            await interaction.response.send_message(
                "Invalid ID format. Please enter a numeric ID.", ephemeral=True
            )
            return

        target_id = int(target_id_str)
        guild = self.view.original_view.interaction.guild
        target = guild.get_role(target_id) or guild.get_member(target_id)

        if not target:
            await interaction.response.send_message(
                f"Could not find a role or user with ID `{target_id}`.", ephemeral=True
            )
            return

        # Open the permission edit modal for this new target
        perm_modal = PermissionEditModal(self.view, target)
        await interaction.response.send_modal(perm_modal)


class PermissionEditModal(discord.ui.Modal, title="Edit Permissions"):
    def __init__(
        self,
        view: "PermissionsEditorView",
        target: Union[discord.Role, discord.Member],
        overwrite: Optional[discord.PermissionOverwrite] = None,
    ):
        super().__init__()
        self.view = view
        self.target = target
        self.overwrite = overwrite or discord.PermissionOverwrite()

        self.permissions_input = discord.ui.TextInput(
            label=f"Permissions for {target.name}",
            placeholder="e.g., send_messages=true, connect=false",
            default=self._overwrite_to_string(self.overwrite),
            style=discord.TextStyle.paragraph,
            required=False,  # Allow submitting empty to clear
        )
        self.add_item(self.permissions_input)

    def _overwrite_to_string(self, overwrite: discord.PermissionOverwrite) -> str:
        """Converts a PermissionOverwrite object to a string for the text input."""
        if not list(iter(overwrite)):
            return ""
        return ", ".join(f"{perm}={value}" for perm, value in iter(overwrite))

    def _string_to_overwrite(
        self, input_str: str
    ) -> Tuple[discord.PermissionOverwrite, List[str]]:
        """Converts the input string back to a PermissionOverwrite object."""
        new_overwrite = discord.PermissionOverwrite()  # Start fresh
        errors = []

        if not input_str:
            return new_overwrite, errors

        pairs = [p.strip() for p in input_str.split(",")]
        for pair in pairs:
            if not pair:
                continue
            parts = pair.split("=")
            if len(parts) != 2:
                errors.append(f"Invalid format: `{pair}`")
                continue

            perm, value_str = parts[0].strip(), parts[1].strip().lower()

            if perm not in discord.Permissions.VALID_FLAGS:
                errors.append(f"Invalid permission: `{perm}`")
                continue

            if value_str in ("true", "t", "yes", "1"):
                value = True
            elif value_str in ("false", "f", "no", "0"):
                value = False
            elif value_str in ("none", "null", "unset", "default"):
                value = None
            else:
                errors.append(f"Invalid value for `{perm}`: `{value_str}`")
                continue

            setattr(new_overwrite, perm, value)

        return new_overwrite, errors

    async def on_submit(self, interaction: discord.Interaction):
        new_overwrite, errors = self._string_to_overwrite(
            self.permissions_input.value
        )
        if errors:
            await interaction.response.send_message(
                "Errors found:\n- " + "\n- ".join(errors), ephemeral=True
            )
            return

        await self.view.stage_permission_change(
            interaction, self.target, new_overwrite
        )


class PermissionsEditorView(discord.ui.View):
    """A view to manage permission overwrites for a channel/category."""

    def __init__(
        self,
        original_view: "ChannelManagerView",
        item: Union[discord.abc.GuildChannel, SimpleNamespace],
    ):
        super().__init__(timeout=300)
        self.original_view = original_view
        self.item = item
        self._build_view()

    def _get_overwrite_for(
        self, target: Union[discord.Role, discord.Member]
    ) -> Optional[discord.PermissionOverwrite]:
        """Helper to get an overwrite from either a real object or our preview dict."""
        if isinstance(self.item, SimpleNamespace):
            return self.item.overwrites.get(target)
        else:
            return self.item.overwrites_for(target)

    def _build_view(self):
        self.clear_items()

        options = []
        # Use self.item.overwrites which works for both SimpleNamespace and discord objects
        sorted_overwrites = sorted(
            self.item.overwrites.items(), key=lambda x: x[0].name
        )

        for target, overwrite in sorted_overwrites:
            target_type = "Role" if isinstance(target, discord.Role) else "User"
            target_name = f"{target_type}: {target.name}"
            options.append(
                discord.SelectOption(label=target_name, value=str(target.id))
            )

        if options:
            select = discord.ui.Select(
                placeholder="Select existing overwrite to edit", options=options, row=0
            )
            select.callback = self.select_overwrite_callback
            self.add_item(select)

        add_button = discord.ui.Button(
            label="Add Overwrite", style=discord.ButtonStyle.green, row=1
        )
        add_button.callback = self.add_overwrite_callback
        self.add_item(add_button)

        back_button = discord.ui.Button(
            label="Back to Manager", style=discord.ButtonStyle.secondary, row=1
        )
        back_button.callback = self.back_callback
        self.add_item(back_button)

    async def select_overwrite_callback(self, interaction: discord.Interaction):
        select = self.children[0]
        target_id = int(interaction.data["values"][0])
        guild = self.original_view.interaction.guild
        target = guild.get_role(target_id) or guild.get_member(target_id)
        if target:
            overwrite = self._get_overwrite_for(target)  # Use helper
            modal = PermissionEditModal(self, target, overwrite)
            await interaction.response.send_modal(modal)
        else:
            await interaction.response.send_message(
                "Could not find the selected target.", ephemeral=True
            )

    async def add_overwrite_callback(self, interaction: discord.Interaction):
        modal = SelectTargetModal(self)
        await interaction.response.send_modal(modal)

    async def back_callback(self, interaction: discord.Interaction):
        await self.original_view.update_view(interaction)
        self.stop()

    async def stage_permission_change(
        self,
        interaction: discord.Interaction,
        target: Union[discord.Role, discord.Member],
        overwrite: discord.PermissionOverwrite,
    ):
        """Stages the permission change in the main view's state."""
        overwrite_dict = {perm: value for perm, value in iter(overwrite)}

        change = {
            "channel_id": self.item.id,
            "target_id": target.id,
            "target_type": "role" if isinstance(target, discord.Role) else "member",
            "overwrite": overwrite_dict,
        }

        if "permissions" not in self.original_view.staged_changes:
            self.original_view.staged_changes["permissions"] = []

        # Remove any existing change for this channel/target pair
        self.original_view.staged_changes["permissions"] = [
            p
            for p in self.original_view.staged_changes["permissions"]
            if not (p["channel_id"] == self.item.id and p["target_id"] == target.id)
        ]

        # Add the new change if it's not empty
        if overwrite_dict:
            self.original_view.staged_changes["permissions"].append(change)

        await self.original_view.update_view(interaction)
        self.stop()


class MoveCategorySelect(discord.ui.Select):
    """Dropdown to select a new category for a channel."""

    def __init__(
        self,
        guild: discord.Guild,
        staged_changes: Dict[str, List[Any]],
        placeholder: str = "Select a new category...",
    ):
        preview_structure = _get_preview_structure(guild, staged_changes)

        options = [
            discord.SelectOption(
                label="(No Category)",
                value="none",
                description="Move channel out of any category.",
            )
        ]
        for category_info in preview_structure:
            if category_info["id"] is None:
                continue  # Skip the 'No Category' group itself

            label = f"📁 {category_info['name']}"
            desc = "Move to this category"
            if category_info["is_new"]:
                label += " (New)"
                desc = "Move to newly created category"

            options.append(
                discord.SelectOption(
                    label=label, value=str(category_info["id"]), description=desc
                )
            )

        super().__init__(
            placeholder=placeholder, min_values=1, max_values=1, options=options
        )

    async def callback(self, interaction: discord.Interaction):
        await self.view.handle_category_selection(interaction, self.values[0])


class MoveItemView(discord.ui.View):
    """A view to handle moving a channel to a new category."""

    def __init__(self, original_view: "ChannelManagerView"):
        super().__init__(timeout=180)
        self.original_view = original_view
        self.guild = original_view.interaction.guild
        staged_changes = original_view.staged_changes

        self.add_item(MoveCategorySelect(self.guild, staged_changes))

    async def handle_category_selection(
        self, interaction: discord.Interaction, selected_value: str
    ):
        """Callback for when a new category is selected."""
        if selected_value.startswith("staged_"):
            # This logic is now handled by passing the ID directly.
            # We find the original name from the preview structure if needed.
            preview = _get_preview_structure(
                self.guild, self.original_view.staged_changes
            )
            cat_info = next(
                (c for c in preview if str(c["id"]) == selected_value), None
            )
            if cat_info:
                await self.original_view.stage_item_move(
                    interaction, new_category_name=cat_info["name"]
                )
        else:
            new_category_id = (
                int(selected_value) if selected_value != "none" else None
            )
            await self.original_view.stage_item_move(
                interaction, new_category_id=new_category_id
            )

    @discord.ui.button(label="Cancel Move", style=discord.ButtonStyle.secondary, row=1)
    async def cancel(self, interaction: discord.Interaction, button: discord.ui.Button):
        await self.original_view.update_view(interaction)
        self.stop()


class ChannelSelect(discord.ui.Select):
    """Dropdown to select a channel or category."""

    def __init__(self, options: List[discord.SelectOption], placeholder: str, row: int):
        if not options:
            options = [
                discord.SelectOption(
                    label="No items to select", value="_placeholder", disabled=True
                )
            ]

        super().__init__(
            placeholder=placeholder,
            min_values=1,
            max_values=1,
            options=options,
            row=row,
            disabled=len(options) == 1 and options[0].value == "_placeholder",
        )

    async def callback(self, interaction: discord.Interaction):
        if self.values[0] == "_placeholder":
            await interaction.response.defer()
            return
        await self.view.handle_selection(interaction, self.values[0])


class ChannelManagerView(discord.ui.View):
    """The main view for the channel manager."""

    def __init__(
        self,
        bot: commands.Bot,
        interaction: discord.Interaction,
        staged_changes: Dict[str, List[Any]],
    ):
        super().__init__(timeout=300)
        self.bot = bot
        self.interaction = interaction
        self.staged_changes = staged_changes
        self.selected_item_id: Optional[Union[int, str]] = None
        self.selected_item_type: Optional[str] = None
 
        # --- Define all buttons ---
        self.create_channel_button = discord.ui.Button(
            label="Create Text Channel", style=discord.ButtonStyle.green, row=0
        )
        self.create_channel_button.callback = self.create_channel_callback
        self.create_voice_channel_button = discord.ui.Button(
            label="Create Voice Channel", style=discord.ButtonStyle.green, row=0
        )
        self.create_voice_channel_button.callback = self.create_voice_channel_callback
        self.create_category_button = discord.ui.Button(
            label="Create Category", style=discord.ButtonStyle.green, row=0
        )
        self.create_category_button.callback = self.create_category_callback
        self.apply_changes_button = discord.ui.Button(
            label="Apply Changes", style=discord.ButtonStyle.primary, row=1
        )
        self.apply_changes_button.callback = self.apply_changes_callback
        self.cancel_button = discord.ui.Button(
            label="Cancel", style=discord.ButtonStyle.danger, row=1
        )
        self.cancel_button.callback = self.cancel_callback
        self.edit_button = discord.ui.Button(
            label="Edit", style=discord.ButtonStyle.secondary, row=0
        )
        self.edit_button.callback = self.edit_callback
        self.move_button = discord.ui.Button(
            label="Move", style=discord.ButtonStyle.secondary, row=0
        )
        self.move_button.callback = self.move_callback
        self.delete_button = discord.ui.Button(
            label="Delete", style=discord.ButtonStyle.danger, row=0
        )
        self.delete_button.callback = self.delete_callback
        self.permissions_button = discord.ui.Button(
            label="Permissions", style=discord.ButtonStyle.secondary, row=1
        )
        self.permissions_button.callback = self.permissions_callback
        self.move_up_button = discord.ui.Button(
            label="Move Up", style=discord.ButtonStyle.secondary, row=1, emoji="⬆️"
        )
        self.move_up_button.callback = self.move_up_callback
        self.move_down_button = discord.ui.Button(
            label="Move Down", style=discord.ButtonStyle.secondary, row=1, emoji="⬇️"
        )
        self.move_down_button.callback = self.move_down_callback
        self.back_button = discord.ui.Button(
            label="Back", style=discord.ButtonStyle.secondary, row=2
        )
        self.back_button.callback = self.back_callback
 
        self._build_view_items(self.interaction.guild)

    async def on_timeout(self):
        key = (self.interaction.guild_id, self.interaction.user.id)
        cog = self.bot.get_cog("ChannelManagerCog")
        if cog:
            cog.staged_changes.pop(key, None)
            cog.active_views.pop(key, None)
        try:
            message = await self.interaction.original_response()
            await message.edit(content="Channel manager session timed out.", view=None, embed=None)
        except discord.HTTPException:
            pass # Message might not be there anymore
        self.stop()

    async def refresh_view(self):
        """Refreshes the view message with the current state without a new interaction."""
        self._build_view_items(self.interaction.guild)
        embed = _generate_channel_tree_embed(self.interaction.guild, self.staged_changes)
        try:
            original_message = await self.interaction.original_response()
            await original_message.edit(embed=embed, view=self)
        except discord.HTTPException as e:
            logger.error(f"Failed to refresh ChannelManagerView: {e}")
 

    def _add_channel_selects(self, guild: discord.Guild):
        """Adds the necessary channel select dropdowns to the view from the preview."""
        all_options = []
        preview_structure = _get_preview_structure(guild, self.staged_changes)

        for category_info in preview_structure:
            if category_info["id"] is not None:
                desc = "Category"
                if category_info["is_new"]:
                    desc += " (New)"
                all_options.append(
                    discord.SelectOption(
                        label=f"📁 {category_info['name']}",
                        value=f"category_{category_info['id']}",
                        description=desc,
                    )
                )

            for channel_info in category_info["channels"]:
                emoji = "🔊" if channel_info["type"] == "voice" else "📝"
                desc = (
                    "Voice Channel"
                    if channel_info["type"] == "voice"
                    else "Text Channel"
                )
                if channel_info["is_new"]:
                    desc += " (New)"
                all_options.append(
                    discord.SelectOption(
                        label=f"  {channel_info['name']}",
                        value=f"channel_{channel_info['id']}",
                        description=desc,
                        emoji=emoji,
                    )
                )

        option_chunks = [
            all_options[i : i + 25] for i in range(0, len(all_options), 25)
        ]
        start_row = 3 if self.selected_item_id else 2
        for i, chunk in enumerate(option_chunks):
            if i >= (5 - start_row):  # Max 5 rows total
                logger.warning(
                    f"Guild {guild.id} has too many items to display ({len(all_options)}). Truncating."
                )
                break
            placeholder = (
                f"Select item ({i*25+1}-{i*25+len(chunk)} of {len(all_options)})..."
            )
            self.add_item(
                ChannelSelect(options=chunk, placeholder=placeholder, row=i + start_row)
            )

    def _build_view_items(self, guild: discord.Guild):
        """Clears and rebuilds the view's items based on the current state."""
        self.clear_items()

        if self.selected_item_id:
            self.add_item(self.edit_button)
            self.add_item(self.move_button)
            self.add_item(self.delete_button)
            self.add_item(self.permissions_button)
            self.add_item(self.move_up_button)
            self.add_item(self.move_down_button)
            self.add_item(self.back_button)
        else:
            self.add_item(self.create_channel_button)
            self.add_item(self.create_voice_channel_button)
            self.add_item(self.create_category_button)

        self.add_item(self.apply_changes_button)
        self.add_item(self.cancel_button)
        self._add_channel_selects(guild)

    async def update_view(self, interaction: discord.Interaction):
        """Update the view with the latest changes and controls."""
        self._build_view_items(interaction.guild)
        embed = _generate_channel_tree_embed(interaction.guild, self.staged_changes)
        await interaction.response.edit_message(embed=embed, view=self)
 
    async def back_callback(self, interaction: discord.Interaction):
        """Go back to the main view by deselecting the current item."""
        self.selected_item_id = None
        self.selected_item_type = None
        await self.update_view(interaction)
 
    async def handle_selection(self, interaction: discord.Interaction, value: str):
        """Handle the selection from the dropdown."""
        item_type, item_id_str = value.split("_", 1)
        self.selected_item_type = item_type
        try:
            self.selected_item_id = int(item_id_str)
        except ValueError:
            self.selected_item_id = item_id_str  # Keep as string for staged items
        await self.update_view(interaction)

    def _get_selected_item_info(self, guild: discord.Guild) -> Optional[Dict]:
        """Finds the full data for the selected item from the preview structure."""
        if not self.selected_item_id:
            return None
        preview = _get_preview_structure(guild, self.staged_changes)
        for cat in preview:
            if cat["id"] == self.selected_item_id:
                return cat
            for chan in cat["channels"]:
                if chan["id"] == self.selected_item_id:
                    return chan
        return None

    async def stage_item_edit(
        self, interaction: discord.Interaction, modal: discord.ui.Modal
    ):
        """Stage a change from a modal submission."""
        is_editing = False
        item = None
        if isinstance(modal, ChannelEditModal):
            is_editing = modal.channel is not None
            item = modal.channel
        elif isinstance(modal, CategoryEditModal):
            is_editing = modal.category is not None
            item = modal.category

        if is_editing:
            change = {"id": item.id, "name": modal.name_input.value}
            if isinstance(modal, ChannelEditModal) and not modal.is_voice:
                change["topic"] = modal.topic_input.value
                nsfw_val = modal.nsfw_input.value.strip().lower()
                if nsfw_val in ("true", "t", "1", "yes", "y"):
                    change["nsfw"] = True
                elif nsfw_val in ("false", "f", "0", "no", "n", ""):
                    change["nsfw"] = False
                slowmode_str = modal.slowmode_input.value.strip()
                if slowmode_str.isdigit():
                    change["slowmode_delay"] = int(slowmode_str)
            elif isinstance(modal, ChannelEditModal) and modal.is_voice:
                bitrate_str = modal.bitrate_input.value.strip()
                if bitrate_str.isdigit():
                    change["bitrate"] = int(bitrate_str) * 1000
                user_limit_str = modal.user_limit_input.value.strip()
                if user_limit_str.isdigit():
                    change["user_limit"] = int(user_limit_str)
            self.staged_changes["edits"].append(change)
        else:  # Creating
            if isinstance(modal, CategoryEditModal):
                self.staged_changes["creations"].append(
                    {"type": "category", "name": modal.name_input.value}
                )
            elif isinstance(modal, ChannelEditModal):
                change = {
                    "type": "channel",
                    "name": modal.name_input.value,
                    "is_voice": modal.is_voice,
                }
                if not modal.is_voice:
                    change["topic"] = modal.topic_input.value
                    nsfw_val = modal.nsfw_input.value.strip().lower()
                    change["nsfw"] = nsfw_val in ("true", "t", "1", "yes", "y")
                    slowmode_str = modal.slowmode_input.value.strip()
                    if slowmode_str.isdigit():
                        change["slowmode_delay"] = int(slowmode_str)
                elif modal.is_voice:
                    bitrate_str = modal.bitrate_input.value.strip()
                    if bitrate_str.isdigit():
                        change["bitrate"] = int(bitrate_str) * 1000
                    user_limit_str = modal.user_limit_input.value.strip()
                    if user_limit_str.isdigit():
                        change["user_limit"] = int(user_limit_str)
                self.staged_changes["creations"].append(change)

        await self.update_view(interaction)

    async def stage_item_delete(self, interaction: discord.Interaction):
        """Stage an item for deletion."""
        self.staged_changes["deletions"].append(
            {"id": self.selected_item_id, "type": self.selected_item_type}
        )
        self.selected_item_id = None
        self.selected_item_type = None
        await self.update_view(interaction)

    async def stage_item_move(
        self,
        interaction: discord.Interaction,
        new_category_id: Optional[Union[int, str]] = None,
        new_category_name: Optional[str] = None,
    ):
        """Stages a channel to be moved to a new category."""
        if "moves" not in self.staged_changes:
            self.staged_changes["moves"] = []

        self.staged_changes["moves"] = [
            m for m in self.staged_changes["moves"] if m["id"] != self.selected_item_id
        ]
        change = {
            "id": self.selected_item_id,
            "type": self.selected_item_type,
            "new_category_id": new_category_id,
            "new_category_name": new_category_name,
        }
        self.staged_changes["moves"].append(change)
        self.selected_item_id = None
        self.selected_item_type = None
        await self.update_view(interaction)

    async def stage_item_position_change(
        self, interaction: discord.Interaction, direction: str
    ):
        """Stages a position change ('up' or 'down')."""
        if "positions" not in self.staged_changes:
            self.staged_changes["positions"] = []
        change = {
            "id": self.selected_item_id,
            "type": self.selected_item_type,
            "direction": direction,
        }
        self.staged_changes["positions"].append(change)
        await self.update_view(interaction)

    # --- Button Callbacks ---
    async def create_channel_callback(self, interaction: discord.Interaction):
        modal = ChannelEditModal(self)
        await interaction.response.send_modal(modal)

    async def create_voice_channel_callback(self, interaction: discord.Interaction):
        modal = ChannelEditModal(self, is_voice=True)
        await interaction.response.send_modal(modal)

    async def create_category_callback(self, interaction: discord.Interaction):
        modal = CategoryEditModal(self)
        await interaction.response.send_modal(modal)

    async def apply_changes_callback(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True, thinking=True)
        cog = self.bot.get_cog("ChannelManagerCog")
        if not cog:
            self.stop()
            return
        summary_embed = await cog.apply_staged_changes(interaction)
        await interaction.followup.send(embed=summary_embed, ephemeral=True)
        
        key = (interaction.guild_id, interaction.user.id)
        cog.active_views.pop(key, None)
        self.stop()

    async def cancel_callback(self, interaction: discord.Interaction):
        key = (interaction.guild_id, interaction.user.id)
        cog = self.bot.get_cog("ChannelManagerCog")
        if cog:
            cog.staged_changes.pop(key, None)
            cog.active_views.pop(key, None)
        await interaction.response.edit_message(
            content="Changes cancelled.", view=None, embed=None
        )
        self.stop()

    async def edit_callback(self, interaction: discord.Interaction):
        item_info = self._get_selected_item_info(interaction.guild)
        if not item_info:
            await interaction.response.send_message(
                "Please select an item to edit first.", ephemeral=True
            )
            return

        if item_info["is_new"]:
            await interaction.response.send_message(
                "Cannot edit a staged creation. Please delete and recreate it.",
                ephemeral=True,
            )
            return

        if self.selected_item_type == "category":
            modal = CategoryEditModal(self, item_info["original"])
        else:
            is_voice = isinstance(item_info["original"], discord.VoiceChannel)
            modal = ChannelEditModal(self, item_info["original"], is_voice=is_voice)
        await interaction.response.send_modal(modal)

    async def move_callback(self, interaction: discord.Interaction):
        item_info = self._get_selected_item_info(interaction.guild)
        if not item_info:
            await interaction.response.send_message(
                "Please select an item to move first.", ephemeral=True
            )
            return
        if self.selected_item_type != "channel":
            await interaction.response.send_message(
                "Only channels can be moved.", ephemeral=True
            )
            return

        move_view = MoveItemView(self)
        embed = _generate_channel_tree_embed(interaction.guild, self.staged_changes)
        await interaction.response.edit_message(
            content=f"**Moving Channel:** {item_info['mention']}\nSelect its new category below.",
            embed=embed,
            view=move_view,
        )

    async def delete_callback(self, interaction: discord.Interaction):
        item_info = self._get_selected_item_info(interaction.guild)
        if not item_info:
            await interaction.response.send_message(
                "Please select an item to delete first.", ephemeral=True
            )
            return

        confirm_view = ConfirmDeleteView(self)
        await interaction.response.send_message(
            f"Are you sure you want to delete **{item_info['name']}**?",
            view=confirm_view,
            ephemeral=True,
        )

    async def permissions_callback(self, interaction: discord.Interaction):
        item_info = self._get_selected_item_info(interaction.guild)
        if not item_info:
            await interaction.response.send_message(
                "Please select an item first.", ephemeral=True
            )
            return

        item_for_view = SimpleNamespace(**item_info)
        perm_view = PermissionsEditorView(self, item_for_view)
        await interaction.response.edit_message(
            content=f"**Editing Permissions for:** {item_for_view.mention}",
            embed=None,
            view=perm_view,
        )

    async def move_up_callback(self, interaction: discord.Interaction):
        if not self.selected_item_id:
            return
        await self.stage_item_position_change(interaction, "up")

    async def move_down_callback(self, interaction: discord.Interaction):
        if not self.selected_item_id:
            return
        await self.stage_item_position_change(interaction, "down")


class ChannelManagerCog(commands.Cog):
    """A cog for managing server channels and categories with a visual interface."""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.staged_changes: Dict[Tuple[int, int], Dict[str, List[Any]]] = {}
        self.active_views: Dict[Tuple[int, int], "ChannelManagerView"] = {}

    async def _send_temp_message(self, interaction: discord.Interaction, content: str, is_error: bool = False):
        """Sends an ephemeral followup message."""
        prefix = "❌" if is_error else "✅"
        try:
            # Send an ephemeral followup to the original interaction
            await interaction.followup.send(f"{prefix} {content}", ephemeral=True)
        except discord.HTTPException as e:
            logger.error(f"Failed to send ephemeral followup: {e}")

    async def apply_staged_changes(
        self, interaction: discord.Interaction
    ) -> discord.Embed:
        """Applies the user's staged changes to the guild."""
        guild = interaction.guild
        key = (guild.id, interaction.user.id)
        changes = self.staged_changes.get(key)

        if not changes or not any(changes.values()):
            return discord.Embed(
                title="No Changes to Apply",
                description="You haven't staged any changes.",
                color=discord.Color.yellow(),
            )

        summary = ["### ✅ Changes Applied"]
        reason = f"Channel Manager action by {interaction.user}"

        # --- 1. Deletions ---
        if changes.get("deletions"):
            summary.append("\n**Deletions:**")
            for deletion in changes["deletions"]:
                item = guild.get_channel(deletion["id"])
                if item:
                    try:
                        await item.delete(reason=reason)
                        summary.append(f"🗑️ Deleted `{item.name}`")
                    except discord.Forbidden:
                        summary.append(f"❌ Failed to delete `{item.name}` (Forbidden)")
                    except discord.HTTPException as e:
                        summary.append(f"❌ Failed to delete `{item.name}` ({e})")
                else:
                    summary.append(
                        f"⚠️ Could not find item `{deletion['id']}` to delete."
                    )

        # --- 2. Creations ---
        newly_created_items = {}  # Maps original name to new discord object
        if changes.get("creations"):
            summary.append("\n**Creations:**")
            # Create categories first
            for creation in filter(
                lambda c: c["type"] == "category", changes["creations"]
            ):
                try:
                    cat = await guild.create_category(
                        name=creation["name"], reason=reason
                    )
                    newly_created_items[creation["name"]] = cat
                    summary.append(f"➕ Created category `{cat.name}`")
                except Exception as e:
                    summary.append(
                        f"❌ Failed to create category `{creation['name']}` ({e})"
                    )
            # Create channels
            for creation in filter(
                lambda c: c["type"] == "channel", changes["creations"]
            ):
                try:
                    if creation.get("is_voice"):
                        chan = await guild.create_voice_channel(
                            name=creation["name"],
                            bitrate=creation.get("bitrate", 64000),
                            user_limit=creation.get("user_limit", 0),
                            reason=reason,
                        )
                    else:
                        chan = await guild.create_text_channel(
                            name=creation["name"],
                            topic=creation.get("topic"),
                            nsfw=creation.get("nsfw", False),
                            slowmode_delay=creation.get("slowmode_delay", 0),
                            reason=reason,
                        )
                    newly_created_items[creation["name"]] = chan
                    summary.append(f"➕ Created channel `#{creation['name']}`")
                except Exception as e:
                    summary.append(
                        f"❌ Failed to create channel `#{creation['name']}` ({e})"
                    )
        
        # Refresh guild object to ensure new channels are available for next steps
        await self.bot.wait_for("guild_channel_create", timeout=5.0, check=lambda c: c.guild.id == guild.id and c.name in [cr['name'] for cr in changes.get("creations", [])])
        guild = self.bot.get_guild(guild.id)


        # --- 3. Edits & Moves (Combined) ---
        # Process edits on existing channels
        if changes.get("edits"):
            summary.append("\n**Edits:**")
            for edit in changes["edits"]:
                item = guild.get_channel(edit["id"])
                if item:
                    try:
                        edit_kwargs = {"name": edit["name"]}
                        if isinstance(item, discord.TextChannel):
                            if "topic" in edit:
                                edit_kwargs["topic"] = edit["topic"]
                            if "nsfw" in edit:
                                edit_kwargs["nsfw"] = edit["nsfw"]
                            if "slowmode_delay" in edit:
                                edit_kwargs["slowmode_delay"] = edit["slowmode_delay"]
                        elif isinstance(item, discord.VoiceChannel):
                            if "bitrate" in edit:
                                edit_kwargs["bitrate"] = edit["bitrate"]
                            if "user_limit" in edit:
                                edit_kwargs["user_limit"] = edit["user_limit"]
                        await item.edit(**edit_kwargs, reason=reason)
                        summary.append(f"✏️ Edited `{item.name}`")
                    except Exception as e:
                        summary.append(f"❌ Failed to edit `{item.name}` ({e})")
                else:
                    summary.append(f"⚠️ Could not find item `{edit['id']}` to edit.")

        # Process moves
        if changes.get("moves"):
            summary.append("\n**Moves:**")
            for move in changes["moves"]:
                item = guild.get_channel(move["id"])
                new_category = None
                if move.get("new_category_id") is not None:
                    new_category = guild.get_channel(move["new_category_id"])
                elif move.get("new_category_name"):
                    new_category = newly_created_items.get(move["new_category_name"])

                if item and isinstance(item, (discord.TextChannel, discord.VoiceChannel)):
                    try:
                        await item.edit(category=new_category, reason=reason)
                        cat_name = f"`{new_category.name}`" if new_category else "`None`"
                        summary.append(f"🚚 Moved `{item.name}` to category {cat_name}")
                    except Exception as e:
                        summary.append(f"❌ Failed to move `{item.name}` ({e})")
                elif not item:
                    summary.append(f"⚠️ Could not find item `{move['id']}` to move.")

        # --- 4. Positions ---
        if changes.get("positions"):
            summary.append("\n**Position Changes:**")
            try:
                # Use the preview structure to get the final order
                final_structure = _get_preview_structure(guild, changes)
                positions = {}
                pos_counter = 0
                for cat_info in final_structure:
                    if cat_info["id"] is not None:
                        positions[cat_info["id"]] = pos_counter
                        pos_counter += 1
                for cat_info in final_structure:
                    for chan_info in cat_info["channels"]:
                        positions[chan_info["id"]] = pos_counter
                        pos_counter += 1
                
                # Convert to format expected by discord.py
                final_payload = {guild.get_channel(k): v for k,v in positions.items() if guild.get_channel(k)}
                await guild.edit_channel_positions(positions=final_payload, reason=reason)
                summary.append("✅ Applied all channel and category position changes.")
            except Exception as e:
                summary.append(f"❌ Failed to apply position changes: {e}")

        # --- 5. Permissions ---
        if changes.get("permissions"):
            summary.append("\n**Permission Overwrites:**")
            for perm_change in changes["permissions"]:
                # Find the item, which could be newly created
                item = guild.get_channel(perm_change["channel_id"])
                target = guild.get_role(perm_change["target_id"]) or guild.get_member(
                    perm_change["target_id"]
                )
                if not item:
                    summary.append(f"⚠️ Could not find channel for perm change.")
                    continue
                if not target:
                    summary.append(f"⚠️ Could not find role/member for perm change.")
                    continue

                try:
                    overwrite = discord.PermissionOverwrite(**perm_change["overwrite"])
                    await item.set_permissions(target, overwrite=overwrite, reason=reason)
                    summary.append(
                        f"🔒 Set permissions for `{target.name}` on `{item.name}`"
                    )
                except Exception as e:
                    summary.append(
                        f"❌ Failed to set perms for `{target.name}` on `{item.name}` ({e})"
                    )

        # --- Cleanup ---
        self.staged_changes.pop(key, None)

        return discord.Embed(
            title="Channel Manager Report",
            description="\n".join(summary),
            color=discord.Color.green(),
        )

    def get_user_staged_changes(
        self, interaction: discord.Interaction
    ) -> Dict[str, List[Any]]:
        """Gets the staged changes for a user, creating them if they don't exist."""
        key = (interaction.guild_id, interaction.user.id)
        if key not in self.staged_changes:
            self.staged_changes[key] = {
                "creations": [],
                "edits": [],
                "deletions": [],
                "moves": [],
                "permissions": [],
                "positions": [],
            }
        return self.staged_changes[key]

    @app_commands.command(
        name="channel_manager", description="Open the channel manager interface."
    )
    @app_commands.checks.has_permissions(manage_channels=True)
    async def manage_channels(self, interaction: discord.Interaction):
        """The main command to open the channel management view."""
        if not interaction.guild:
            await interaction.response.send_message(
                "This command can only be used in a server.", ephemeral=True
            )
            return

        key = (interaction.guild_id, interaction.user.id)
        if key in self.active_views:
            try:
                old_view = self.active_views[key]
                msg = await old_view.interaction.original_response()
                await interaction.response.send_message(
                    f"You already have a channel manager open: {msg.jump_url}",
                    ephemeral=True,
                )
                return
            except discord.HTTPException:
                self.active_views.pop(key, None)

        staged_changes = self.get_user_staged_changes(interaction)

        embed = _generate_channel_tree_embed(interaction.guild, staged_changes)
        view = ChannelManagerView(self.bot, interaction, staged_changes)
        self.active_views[key] = view

        await interaction.response.send_message(embed=embed, view=view, ephemeral=True)


    def _find_items_by_name(self, guild: discord.Guild, staged_changes: Dict, name: str) -> List[Dict]:
        """Finds items (channels or categories) by name from the preview structure, prioritizing exact matches."""
        preview = _get_preview_structure(guild, staged_changes)
        name_lower = name.lower()
        exact_matches = []
        partial_matches = []

        for cat_info in preview:
            # Check the category itself
            if cat_info.get("name"):
                if cat_info["name"].lower() == name_lower:
                    exact_matches.append(cat_info)
                elif name_lower in cat_info["name"].lower():
                    partial_matches.append(cat_info)

            # Check channels within the category
            for chan_info in cat_info.get("channels", []):
                if chan_info["name"].lower() == name_lower:
                    exact_matches.append(chan_info)
                elif name_lower in chan_info["name"].lower():
                    partial_matches.append(chan_info)

        return exact_matches if exact_matches else partial_matches

    @commands.Cog.listener()
    async def on_message(self, message: discord.Message):
        if message.author.bot or not message.guild:
            return

        key = (message.guild.id, message.author.id)
        if key not in self.active_views:
            return

        view = self.active_views[key]
        content = message.content.strip()

        try:
            await message.delete()
        except discord.Forbidden:
            logger.warning(f"Could not delete command message in {message.channel.id}")
        except discord.HTTPException as e:
            logger.error(f"Failed to delete message: {e}")

        try:
            parts = shlex.split(content)
        except ValueError:
            await self._send_temp_message(
                view.interaction, "Invalid command format. Check your quotes.", is_error=True
            )
            return

        if not parts:
            return

        command = parts[0].lower()
        args = parts[1:]

        # --- Command Parsing ---
        if command in ("mk", "make", "create"):
            if len(args) < 2:
                await self._send_temp_message(
                    view.interaction,
                    'Usage: `mk <type> "name"` (type can be text, voice, category)',
                    is_error=True,
                )
                return
            item_type = args[0].lower()
            name = args[1]

            # Validate item type first before checking for existing items
            change = None
            if item_type in ("channel", "text"):
                change = {"type": "channel", "name": name, "is_voice": False}
            elif item_type == "voice":
                change = {"type": "channel", "name": name, "is_voice": True}
            elif item_type == "category":
                change = {"type": "category", "name": name}
            else:
                await self._send_temp_message(
                    view.interaction,
                    f"Invalid type `{item_type}`. Use text, voice, or category.",
                    is_error=True,
                )
                return

            # Check for existing item with the same name only after validating type
            existing_items = self._find_items_by_name(
                message.guild, view.staged_changes, name
            )
            if existing_items:
                await self._send_temp_message(
                    view.interaction, f"An item named `{name}` already exists.", is_error=True
                )
                return

            if change:
                view.staged_changes["creations"].append(change)
                await view.refresh_view()
                await self._send_temp_message(view.interaction, f"Staged creation of {item_type} `{name}`.")

        elif command in ("mv", "move"):
            if len(args) < 3:
                await self._send_temp_message(
                    view.interaction,
                    'Usage: `mv "item name" <position|category> "target name"`',
                    is_error=True,
                )
                return
            item_name, target_type, target_name = args[0], args[1].lower(), args[2]

            items_to_move = self._find_items_by_name(
                message.guild, view.staged_changes, item_name
            )
            if not items_to_move:
                await self._send_temp_message(
                    view.interaction, f"Could not find item to move: `{item_name}`", is_error=True
                )
                return
            if len(items_to_move) > 1:
                await self._send_temp_message(
                    view.interaction,
                    f"Ambiguous item name `{item_name}`. Found {len(items_to_move)} items.",
                    is_error=True,
                )
                return
            item_to_move = items_to_move[0]

            if target_type == "category":
                if item_to_move["type"] not in ("text", "voice"):
                    await self._send_temp_message(
                        view.interaction,
                        "Only channels can be moved to a category.",
                        is_error=True,
                    )
                    return

                target_cats = self._find_items_by_name(
                    message.guild, view.staged_changes, target_name
                )
                if not target_cats:
                    await self._send_temp_message(
                        view.interaction,
                        f"Could not find target category: `{target_name}`",
                        is_error=True,
                    )
                    return
                if len(target_cats) > 1:
                    await self._send_temp_message(
                        view.interaction,
                        f"Ambiguous category name `{target_name}`. Found {len(target_cats)}.",
                        is_error=True,
                    )
                    return
                new_category = target_cats[0]
                if new_category["type"] != "category":
                    await self._send_temp_message(
                        view.interaction, f"`{target_name}` is not a category.", is_error=True
                    )
                    return

                view.staged_changes["moves"] = [
                    m
                    for m in view.staged_changes.get("moves", [])
                    if m["id"] != item_to_move["id"]
                ]
                view.staged_changes["moves"].append(
                    {
                        "id": item_to_move["id"],
                        "type": "channel",
                        "new_category_id": new_category["id"],
                        "new_category_name": new_category["name"]
                        if new_category["is_new"]
                        else None,
                    }
                )
                await view.refresh_view()
                await self._send_temp_message(view.interaction, f"Staged move for `{item_to_move['name']}`.")


            elif target_type == "position":
                direction = "up" if target_name in ("-1", "up") else "down"
                view.staged_changes["positions"].append(
                    {
                        "id": item_to_move["id"],
                        "type": item_to_move["type"],
                        "direction": direction,
                    }
                )
                await view.refresh_view()
                await self._send_temp_message(view.interaction, f"Staged position change for `{item_to_move['name']}`.")

            else:
                await self._send_temp_message(
                    view.interaction,
                    f"Invalid move type `{target_type}`. Use `category` or `position`.",
                    is_error=True,
                )

        elif command == "edit":
            if len(args) < 3:
                await self._send_temp_message(
                    view.interaction,
                    'Usage: `edit "item name" <attribute> "new value"`',
                    is_error=True,
                )
                return
            item_name, attr, value = args[0], args[1].lower(), " ".join(args[2:])

            items_to_edit = self._find_items_by_name(
                message.guild, view.staged_changes, item_name
            )
            if not items_to_edit:
                await self._send_temp_message(
                    view.interaction, f"Could not find item to edit: `{item_name}`", is_error=True
                )
                return
            if len(items_to_edit) > 1:
                await self._send_temp_message(
                    view.interaction,
                    f"Ambiguous item name `{item_name}`. Found {len(items_to_edit)} items.",
                    is_error=True,
                )
                return
            item_to_edit = items_to_edit[0]

            if item_to_edit["is_new"]:
                await self._send_temp_message(
                    view.interaction,
                    f"Cannot edit `{item_name}` as it's a new, unsaved item. Delete and recreate it if needed.",
                    is_error=True,
                )
                return

            change = {"id": item_to_edit["id"]}
            valid_change = False
            error = None

            if attr == "name":
                # Check for name collisions before staging the edit
                all_items_preview = _get_preview_structure(message.guild, view.staged_changes)
                flat_items = [c for cat in all_items_preview for c in cat.get('channels', [])] + [c for c in all_items_preview if c.get('id') is not None]
                if any(i['name'].lower() == value.lower() and i['id'] != item_to_edit['id'] for i in flat_items):
                    error = f"An item named `{value}` already exists."
                else:
                    change["name"] = value
                    valid_change = True
            elif item_to_edit["type"] in ("text", "voice"):
                if attr == "nsfw" and item_to_edit["type"] == "text":
                    change["nsfw"] = value.lower() in ("true", "t", "yes", "1")
                    valid_change = True
                elif attr == "topic" and item_to_edit["type"] == "text":
                    change["topic"] = value
                    valid_change = True
                elif attr == "slowmode" and item_to_edit["type"] == "text":
                    if value.isdigit():
                        change["slowmode_delay"] = int(value)
                        valid_change = True
                    else:
                        error = "Slowmode must be a number (seconds)."
                elif attr == "bitrate" and item_to_edit["type"] == "voice":
                    if value.isdigit():
                        change["bitrate"] = int(value) * 1000
                        valid_change = True
                    else:
                        error = "Bitrate must be a number (kbps)."
                elif attr == "user_limit" and item_to_edit["type"] == "voice":
                    if value.isdigit():
                        change["user_limit"] = int(value)
                        valid_change = True
                    else:
                        error = "User limit must be a number."
                else:
                    error = f"Invalid attribute `{attr}` for a {item_to_edit['type']} channel."
            else:
                error = f"Invalid attribute `{attr}` for a category."

            if error:
                await self._send_temp_message(view.interaction, error, is_error=True)
            elif valid_change:
                view.staged_changes["edits"].append(change)
                await view.refresh_view()
                await self._send_temp_message(view.interaction, f"Staged edit for `{item_to_edit['name']}`.")
            else:
                await self._send_temp_message(
                    view.interaction, f"Invalid edit attribute or value for `{item_name}`.", is_error=True
                )

        elif command in ("rm", "del", "delete"):
            if not args:
                await self._send_temp_message(
                    view.interaction, 'Usage: `rm "item name"`', is_error=True
                )
                return
            item_name = args[0]
            items_to_delete = self._find_items_by_name(
                message.guild, view.staged_changes, item_name
            )
            if not items_to_delete:
                await self._send_temp_message(
                    view.interaction, f"Could not find item to delete: `{item_name}`", is_error=True
                )
                return
            if len(items_to_delete) > 1:
                await self._send_temp_message(
                    view.interaction,
                    f"Ambiguous item name `{item_name}`. Found {len(items_to_delete)} items.",
                    is_error=True,
                )
                return
            item_to_delete = items_to_delete[0]

            view.staged_changes["deletions"].append(
                {"id": item_to_delete["id"], "type": item_to_delete["type"]}
            )
            await view.refresh_view()
            await self._send_temp_message(view.interaction, f"Staged deletion for `{item_to_delete['name']}`.")


        elif command in ("exit", "quit", "close", "cancel"):
            try:
                original_message = await view.interaction.original_response()
                await original_message.edit(
                    content="Changes discarded. View closed by text command.",
                    view=None,
                    embed=None,
                )
            except discord.HTTPException:
                pass
            self.staged_changes.pop(key, None)
            self.active_views.pop(key, None)
            view.stop()

        elif command in ("apply", "save", "commit"):
            try:
                original_message = await view.interaction.original_response()
                await original_message.edit(
                    content="Applying changes...", view=None, embed=None
                )
                summary_embed = await self.apply_staged_changes(view.interaction)
                await view.interaction.followup.send(embed=summary_embed, ephemeral=True)
                await self._send_temp_message(view.interaction, "Changes applied successfully.")

            except discord.HTTPException as e:
                logger.error(f"Failed to apply changes via text command: {e}")
                await self._send_temp_message(
                    view.interaction, "An error occurred while applying changes.", is_error=True
                )
            finally:
                self.staged_changes.pop(key, None)
                self.active_views.pop(key, None)
                view.stop()


async def setup(bot: commands.Bot):
    await bot.add_cog(ChannelManagerCog(bot))
    logger.info("ChannelManagerCog loaded successfully.")