# cogs/rp_messages.py

MOLEST_MESSAGE_TEMPLATE = """
{target} - Your pants are slowly and deliberately removed, leaving you feeling exposed and vulnerable. The sensation is both thrilling and terrifying as a presence looms over you, the only sound being the faint rustling of fabric as your clothes are discarded.
"""


def get_rape_messages(user_mention: str, target_mention: str) -> list[str]:
    return [
        f"{user_mention} raped {target_mention}.",
        f"{user_mention} brutally raped {target_mention}.",
        f"{user_mention} unconsensually came inside {target_mention}.",
        f"{user_mention} forced themselves onto {target_mention}.",
        f"{user_mention} violated {target_mention} in a grotesque manner.",
        f"{target_mention} was unconsensually defiled by {user_mention}.",
        f"{user_mention} left {target_mention} traumatized after the assault.",
        f"{user_mention} subjected {target_mention} to a horrific ordeal.",
        f"{target_mention} was brutally assaulted by {user_mention}.",
        f"{user_mention} committed an act of sexual violence against {target_mention}.",
        f"{target_mention} was left broken and violated by {user_mention}.",
        f"{user_mention} took {target_mention} by force.",
        f"{user_mention} inflicted unspeakable trauma upon {target_mention}.",
        f"{user_mention} violated {target_mention}'s body and spirit.",
        f"{target_mention} was forced into submission by {user_mention}.",
        f"{user_mention} took what they wanted from {target_mention} without consent.",
        f"{target_mention} endured a horrifying assault by {user_mention}.",
        f"{user_mention} defiled {target_mention} against their will.",
        f"{target_mention} was left shattered by {user_mention}'s actions.",
        f"{user_mention} asserted their dominance over {target_mention} through sexual violence.",
        f"{target_mention} was subjected to {user_mention}'s cruel desires.",
        f"{user_mention} committed an unforgivable act against {target_mention}.",
        f"{target_mention} was used and discarded by {user_mention}.",
        f"{user_mention} left {target_mention} feeling utterly powerless.",
        f"{target_mention} was a victim of {user_mention}'s brutal urges.",
        f"{user_mention} stripped {target_mention} of their dignity.",
        f"{target_mention} was trapped in a nightmare by {user_mention}.",
        f"{user_mention} showed no mercy as they violated {target_mention}.",
        f"{target_mention} was utterly helpless against {user_mention}'s assault.",
        f"{user_mention} reveled in {target_mention}'s pain and fear.",
        f"{user_mention} was broken by {target_mention}'s actions.",
        f"{user_mention} took {target_mention}'s innocence by force.",
        f"{target_mention} was scarred for life by {user_mention}'s brutality.",
        f"{user_mention} left {target_mention} a shell of their former self.",
        f"{target_mention} was a plaything for {user_mention}'s twisted desires.",
        f"{user_mention} crushed {target_mention}'s spirit with their assault.",
        f"{target_mention} was a victim of {user_mention}'s depravity.",
        f"{user_mention} reveled in the non-consensual act with {target_mention}.",
        f"{target_mention} was forced to endure {user_mention}'s unwanted advances.",
        f"{user_mention} took advantage of {target_mention} in the cruelest way.",
        f"{target_mention} was subjected to {user_mention}'s vile acts.",
        f"{user_mention} left {target_mention} in a state of shock and despair.",
        f"{target_mention} was violated by {user_mention} in a way they'll never forget.",
        f"{user_mention} committed a heinous crime against {target_mention}.",
        f"{target_mention} was a pawn in {user_mention}'s sick game.",
        f"{user_mention} showed no regard for {target_mention}'s autonomy.",
        f"{target_mention} was left with deep, unhealing wounds by {user_mention}.",
        f"{user_mention} took pleasure in {target_mention}'s suffering.",
        f"{target_mention} was a victim of {user_mention}'s predatory nature.",
        f"{user_mention} defiled {target_mention} in the most brutal manner.",
        f"{target_mention} was left with nothing but trauma by {user_mention}.",
        f"{user_mention} asserted their power over {target_mention} through sexual assault.",
        f"{target_mention} was a broken mess after {user_mention}'s attack.",
        f"{user_mention} took everything from {target_mention} without a second thought.",
        f"{target_mention} was a mere object to {user_mention}.",
        f"{user_mention} reveled in the destruction of {target_mention}'s innocence.",
        f"{target_mention} was a victim of {user_mention}'s twisted fantasies.",
        f"{user_mention} left {target_mention} with scars that would never fade.",
        f"{target_mention} was forced to endure the unthinkable by {user_mention}.",
        f"{user_mention} committed an act of pure evil against {target_mention}.",
        f"{target_mention} was a shell of their former self after {user_mention}'s violation.",
        f"{user_mention} took {target_mention}'s body and soul.",
        f"{target_mention} was left with a lifetime of pain by {user_mention}.",
        f"{user_mention} showed no remorse for their actions against {target_mention}.",
        f"{target_mention} was a victim of {user_mention}'s monstrous desires.",
        f"{user_mention} defiled {target_mention} in a way that can never be undone.",
        f"{target_mention} was left with an emptiness that could never be filled by {user_mention}.",
        f"{user_mention} asserted their control over {target_mention} through a brutal assault.",
        f"{target_mention} was a broken toy after {user_mention}'s attack.",
        f"{user_mention} took {target_mention}'s purity by force.",
        f"{target_mention} was left with a shattered psyche by {user_mention}.",
        f"{user_mention} reveled in {target_mention}'s screams and tears.",
        f"{target_mention} was a victim of {user_mention}'s sadistic pleasure.",
        f"{user_mention} defiled {target_mention} in the most sickening way imaginable.",
        f"{target_mention} was left with a permanent stain on their soul by {user_mention}.",
        f"{user_mention} asserted their will over {target_mention} with brutal force.",
        f"{target_mention} was a broken spirit after {user_mention}'s violation.",
        f"{user_mention} took {target_mention}'s essence without a thought.",
        f"{target_mention} was a mere vessel for {user_mention}'s dark desires.",
        f"{user_mention} reveled in the complete destruction of {target_mention}.",
        f"{target_mention} was a victim of {user_mention}'s twisted and depraved mind.",
        f"{user_mention} left {target_mention} with wounds that would never heal.",
        f"{target_mention} was forced to endure the ultimate humiliation by {user_mention}.",
        f"{user_mention} committed an act of pure barbarity against {target_mention}.",
        f"{target_mention} was a shadow of their former self after {user_mention}'s assault.",
        f"{user_mention} took {target_mention}'s very being.",
        f"{target_mention} was left with an unbearable burden by {user_mention}.",
        f"{user_mention} showed no humanity in their actions against {target_mention}.",
        f"{target_mention} was a victim of {user_mention}'s monstrous and insatiable lust.",
        f"{user_mention} defiled {target_mention} in a way that will haunt them forever.",
        f"{target_mention} was left with a void that could never be filled by {user_mention}.",
        f"{user_mention} asserted their absolute control over {target_mention} through a horrific act.",
        f"{target_mention} was a shattered mirror after {user_mention}'s attack.",
        f"{user_mention} took {target_mention}'s very soul by force.",
        f"{target_mention} was left with a broken spirit and a ruined life by {user_mention}.",
        f"{user_mention} reveled in {target_mention}'s complete and utter despair.",
        f"{target_mention} was a victim of {user_mention}'s unspeakable cruelty.",
        f"{user_mention} defiled {target_mention} in the most abhorrent way imaginable.",
        f"{target_mention} was left with a darkness that would consume them by {user_mention}.",
        f"{user_mention} asserted their tyrannical will over {target_mention} with extreme prejudice.",
        f"{target_mention} was a broken husk after {user_mention}'s violation.",
        f"{user_mention} took {target_mention}'s last shred of hope.",
        f"{target_mention} was a mere object of {user_mention}'s twisted amusement.",
        f"{user_mention} reveled in the total annihilation of {target_mention}.",
        f"{target_mention} was a victim of {user_mention}'s utterly depraved and evil mind.",
        f"{user_mention} left {target_mention} with wounds that would never, ever heal.",
        f"{target_mention} was forced to endure the absolute worst by {user_mention}.",
        f"{user_mention} committed an act of pure, unadulterated evil against {target_mention}.",
        f"{target_mention} was a ghost of their former self after {user_mention}'s assault.",
        f"{user_mention} took {target_mention}'s very existence.",
        f"{target_mention} was left with an eternal torment by {user_mention}.",
        f"{user_mention} showed no shred of humanity in their actions against {target_mention}.",
        f"{target_mention} was a victim of {user_mention}'s monstrous, insatiable, and utterly vile lust.",
        f"{user_mention} defiled {target_mention} in a way that will haunt them for all eternity.",
        f"{target_mention} was left with a bottomless void that could never, ever be filled by {user_mention}.",
        f"{user_mention} asserted their absolute, tyrannical control over {target_mention} through a horrific, unspeakable act.",
        f"{target_mention} was a shattered, irreparable mirror after {user_mention}'s attack.",
        f"{user_mention} took {target_mention}'s very soul and essence by brutal, unforgiving force.",
        f"{target_mention} was left with a broken spirit, a ruined life, and a shattered psyche by {user_mention}.",
        f"{user_mention} reveled in {target_mention}'s complete, utter, and eternal despair.",
        f"{target_mention} was a victim of {user_mention}'s unspeakable, monstrous, and sadistic cruelty.",
        f"{user_mention} defiled {target_mention} in the most abhorrent, sickening, and vile way imaginable.",
        f"{target_mention} was left with a darkness that would consume them entirely, forever, by {user_mention}.",
        f"{user_mention} asserted their tyrannical, absolute, and unyielding will over {target_mention} with extreme, brutal prejudice.",
        f"{target_mention} was a broken, empty husk after {user_mention}'s violation.",
        f"{user_mention} took {target_mention}'s last shred of hope, dignity, and humanity.",
        f"{target_mention} was a mere object of {user_mention}'s twisted, depraved, and utterly sick amusement.",
        f"{user_mention} reveled in the total, complete, and absolute annihilation of {target_mention}.",
        f"{target_mention} was a victim of {user_mention}'s utterly depraved, evil, and monstrous mind.",
    ]


def get_sex_messages(user_mention: str, target_mention: str) -> list[str]:
    return [
        f"{user_mention} and {target_mention} shared a tender kiss that deepened into a passionate embrace.",
        f"{user_mention} gently caressed {target_mention}'s cheek before their lips met, igniting a spark.",
        f"With a soft touch, {user_mention} guided {target_mention}'s hand to their waist, pulling them closer.",
        f"{user_mention} whispered sweet nothings into {target_mention}'s ear, sending shivers down their spine.",
        f"Their bodies pressed together, {user_mention} and {target_mention} moved in a slow, sensual rhythm.",
        f"{target_mention} moaned softly as {user_mention}'s touch became more intimate.",
        f"{user_mention}'s fingers traced the curve of {target_mention}'s back, eliciting a gasp.",
        f"In the dim light, {user_mention} admired the beauty of {target_mention}'s form.",
        f"Their breaths mingled as {user_mention} and {target_mention} lost themselves in the moment.",
        f"{target_mention}'s legs wrapped around {user_mention}'s waist, pulling them into a deeper connection.",
        f"{user_mention} buried their face in {target_mention}'s neck, inhaling their scent.",
        f"The room filled with soft sounds of pleasure as {user_mention} and {target_mention} explored each other.",
        f"{target_mention}'s fingers tangled in {user_mention}'s hair, holding them close.",
        f"{user_mention}'s hips moved against {target_mention}'s, building a delicious tension.",
        f"With a final, shared sigh, {user_mention} and {target_mention} found release in each other's arms.",
        f"{user_mention} and {target_mention} lay tangled in the sheets, their bodies still humming with the afterglow.",
        f"{target_mention} rested their head on {user_mention}'s chest, listening to their heartbeat.",
        f"{user_mention} kissed {target_mention}'s forehead, a silent promise of more to come.",
        f"The scent of their lovemaking hung in the air as {user_mention} and {target_mention} drifted off to sleep.",
        f"{user_mention} and {target_mention} woke up intertwined, the morning sun casting a warm glow on their bodies.",
        f"{user_mention} and {target_mention} had a passionate night together.",
        f"{user_mention} made love to {target_mention}.",
        f"{target_mention} was pleasured by {user_mention}.",
        f"{user_mention} and {target_mention} shared an intimate moment.",
        f"{user_mention} and {target_mention} explored their desires.",
        f"{target_mention} felt a deep connection with {user_mention} during their encounter.",
        f"{user_mention} and {target_mention} experienced mutual pleasure.",
        f"{user_mention} and {target_mention} explored the depths of their passion, bodies moving as one.",
        f"A symphony of moans filled the air as {user_mention} and {target_mention} surrendered to their desires.",
        f"{user_mention} traced kisses down {target_mention}'s body, igniting fires with every touch.",
        f"{target_mention} arched into {user_mention}'s touch, lost in a world of sensation.",
        f"Their hearts pounded in unison as {user_mention} and {target_mention} reached a blissful peak together.",
        f"Whispers of love and desire were exchanged between {user_mention} and {target_mention} amidst their passionate encounter.",
        f"{user_mention} held {target_mention} close, savoring the intimacy of their shared pleasure.",
        f"The world outside faded away as {user_mention} and {target_mention} became each other's sole focus.",
        f"{target_mention}'s skin tingled under {user_mention}'s expert touch, every nerve alive with pleasure.",
        f"They moved together in a dance of passion, {user_mention} leading {target_mention} to new heights of ecstasy.",
        f"{user_mention} savored the taste of {target_mention}'s lips, a prelude to deeper intimacies.",
        f"The air crackled with electricity as {user_mention} and {target_mention} gave in to their mutual attraction.",
        f"{target_mention} clung to {user_mention}, their bodies intertwined in a loving embrace.",
        f"Every touch, every kiss, deepened the bond between {user_mention} and {target_mention}.",
        f"Lost in each other's eyes, {user_mention} and {target_mention} found a universe in their shared moment.",
    ]


def get_headpat_messages(user_mention: str, target_mention: str) -> list[str]:
    return [
        f"{user_mention} gently pats {target_mention}'s head, a soft smile gracing their lips.",
        f"{user_mention} reaches out and gives {target_mention} a comforting headpat.",
        f"A warm hand from {user_mention} ruffles {target_mention}'s hair with a gentle headpat.",
        f"{user_mention} gives {target_mention} a series of light, affectionate headpats.",
        f"{user_mention} softly strokes {target_mention}'s head, a gesture of warmth and care.",
        f"{user_mention} leans in and gives {target_mention} a tender headpat, making them feel cherished.",
        f"With a loving gaze, {user_mention} gives {target_mention} a reassuring headpat.",
        f"{user_mention} playfully taps {target_mention}'s head, a sign of friendly affection.",
        f"{user_mention} gives {target_mention} a slow, soothing headpat, easing their worries.",
        f"A gentle hand from {user_mention} rests on {target_mention}'s head, offering silent support.",
        f"{user_mention} gives {target_mention} a quick, encouraging headpat.",
        f"{user_mention} runs their fingers through {target_mention}'s hair, ending with a soft headpat.",
        f"{user_mention} gives {target_mention} a congratulatory headpat.",
        f"{user_mention} gives {target_mention} a comforting headpat after a long day.",
        f"{user_mention} gives {target_mention} a headpat that feels like a warm hug.",
        f"{user_mention} gently boops {target_mention}'s head with a soft pat.",
        f"{user_mention} offers {target_mention} a gentle headpat, their eyes full of affection.",
        f"A soft pat on the head from {user_mention} makes {target_mention} feel safe and sound.",
        f"{user_mention} ruffles {target_mention}'s hair playfully, then smooths it down with a kind headpat.",
        f"{target_mention} leans into {user_mention}'s hand as they receive a comforting headpat.",
        f"{user_mention} gives {target_mention} a headpat and a warm smile, brightening their day.",
        f"With a tender touch, {user_mention} pats {target_mention}'s head, conveying unspoken understanding.",
        f"{user_mention}'s headpat is a small gesture, but it means the world to {target_mention}.",
        f"{target_mention} closes their eyes, enjoying the soothing sensation of {user_mention}'s headpat.",
        f"A quick, cheerful headpat from {user_mention} lifts {target_mention}'s spirits.",
        f"{user_mention} gives {target_mention} a 'good job!' headpat, full of pride.",
        f"The world feels a little kinder after a gentle headpat from {user_mention} for {target_mention}.",
        f"{user_mention} carefully pats {target_mention}'s head, as if handling something precious.",
        f"{target_mention} practically purrs under {user_mention}'s affectionate headpat.",
        f"One simple headpat from {user_mention} is enough to make {target_mention} feel appreciated.",
        f"{user_mention} gives {target_mention} a headpat that says 'I'm here for you'.",
    ]


def get_cumshot_messages(user_mention: str, target_mention: str) -> list[str]:
    return [
        f"{user_mention} cums on {target_mention}.",
        f"{user_mention} finishes all over {target_mention}'s face.",
        f"{target_mention} is covered in {user_mention}'s cum.",
        f"{user_mention} unloads a huge load onto {target_mention}.",
        f"{user_mention} cums hard, drenching {target_mention}.",
        f"{user_mention} splatters {target_mention} with a thick load.",
        f"A warm stream from {user_mention} coats {target_mention}.",
        f"{target_mention} receives a generous cumshot from {user_mention}.",
        f"{user_mention}'s cum drips from {target_mention}'s chin.",
        f"{user_mention} leaves {target_mention} sticky and satisfied.",
        f"{user_mention} erupts, painting {target_mention} with their essence.",
        f"{target_mention} is glazed by {user_mention}'s potent release.",
        f"{user_mention} showers {target_mention} with a hot, sticky load.",
        f"A thick rope from {user_mention} lands squarely on {target_mention}.",
        f"{target_mention} gasps as {user_mention} finishes with intensity.",
        f"{user_mention}'s climax leaves {target_mention} beautifully messy.",
        f"{user_mention} covers {target_mention} in a testament to their pleasure.",
        f"The evidence of {user_mention}'s release glistens on {target_mention}'s skin.",
        f"{target_mention} is marked by {user_mention}'s passionate finish.",
        f"{user_mention} groans, releasing a torrent onto {target_mention}.",
        f"{target_mention} feels the warmth of {user_mention}'s cum spreading.",
        f"{user_mention} ensures {target_mention} is thoroughly coated.",
        f"A generous offering from {user_mention} leaves {target_mention} breathless.",
        f"{user_mention} doesn't hold back, dousing {target_mention} completely.",
        f"{target_mention} wears {user_mention}'s cum like a trophy.",
    ]


def get_kiss_messages(user_mention: str, target_mention: str) -> list[str]:
    return [
        f"{user_mention} gives {target_mention} a sweet kiss on the cheek.",
        f"{user_mention} leans in and gives {target_mention} a gentle kiss.",
        f"{user_mention} plants a soft kiss on {target_mention}'s forehead.",
        f"{user_mention} and {target_mention} share a quick, affectionate kiss.",
        f"{user_mention} gives {target_mention} a warm, lingering kiss.",
        f"{user_mention} kisses {target_mention}'s hand tenderly.",
        f"{user_mention} gives {target_mention} a playful peck on the nose.",
        f"{user_mention} and {target_mention} share a loving kiss.",
        f"{user_mention} gives {target_mention} a comforting kiss.",
        f"{user_mention} kisses {target_mention} with a smile.",
        f"{user_mention} gives {target_mention} a butterfly kiss with their eyelashes.",
        f"{user_mention} blows {target_mention} a sweet air kiss.",
        f"{user_mention} gives {target_mention} a tender kiss on the lips.",
        f"{user_mention} surprises {target_mention} with a quick kiss.",
        f"{user_mention} kisses {target_mention}'s fingertips delicately.",
        f"{user_mention} gives {target_mention} an eskimo kiss, rubbing noses.",
        f"{user_mention} plants a loving kiss on {target_mention}'s temple.",
        f"{user_mention} gives {target_mention} a passionate but gentle kiss.",
        f"{user_mention} kisses {target_mention} under the starlight.",
        f"{user_mention} gives {target_mention} a goodnight kiss.",
        f"{user_mention} presses a soft, lingering kiss to {target_mention}'s lips.",
        f"A trail of gentle kisses is left by {user_mention} on {target_mention}'s neck.",
        f"{user_mention} steals a sweet kiss from {target_mention} when they least expect it.",
        f"Their lips meet in a kiss that speaks volumes, {user_mention} and {target_mention}.",
        f"{user_mention} gives {target_mention} a kiss that tastes like sunshine and happiness.",
        f"A tender kiss on the shoulder from {user_mention} makes {target_mention} melt.",
        f"{user_mention} cups {target_mention}'s face gently before delivering a heartfelt kiss.",
        f"The world stops for a moment as {user_mention} and {target_mention} share a perfect kiss.",
        f"{user_mention} gives {target_mention} a 'welcome home' kiss that's full of warmth.",
        f"A sleepy morning kiss is shared between {user_mention} and {target_mention}.",
        f"{user_mention} kisses away {target_mention}'s tears with gentle affection.",
        f"A flurry of tiny kisses from {user_mention} makes {target_mention} giggle.",
        f"{user_mention} gives {target_mention} a kiss that promises adventure.",
        f"Their first kiss was shy, but {user_mention} and {target_mention} knew it was special.",
        f"{user_mention} seals their promise to {target_mention} with a solemn kiss.",
    ]


def get_hug_messages(user_mention: str, target_mention: str) -> list[str]:
    return [
        f"{user_mention} gives {target_mention} a warm hug.",
        f"{user_mention} wraps their arms around {target_mention} in a comforting hug.",
        f"{user_mention} and {target_mention} share a tight hug.",
        f"{user_mention} gives {target_mention} a gentle hug.",
        f"{user_mention} pulls {target_mention} into a loving embrace.",
        f"{user_mention} and {target_mention} share a long, heartfelt hug.",
        f"{user_mention} gives {target_mention} a friendly hug.",
        f"{user_mention} hugs {target_mention} tightly.",
        f"{user_mention} gives {target_mention} a warm, fuzzy hug.",
        f"{user_mention} and {target_mention} share a sweet hug.",
        f"{user_mention} gives {target_mention} a bear hug that lifts them off the ground.",
        f"{user_mention} wraps {target_mention} in a protective embrace.",
        f"{user_mention} gives {target_mention} a surprise hug from behind.",
        f"{user_mention} and {target_mention} share a cozy group hug.",
        f"{user_mention} gives {target_mention} a quick side hug.",
        f"{user_mention} embraces {target_mention} with open arms.",
        f"{user_mention} gives {target_mention} a reassuring hug.",
        f"{user_mention} squeezes {target_mention} in a playful hug.",
        f"{user_mention} gives {target_mention} a healing hug that makes everything better.",
        f"{user_mention} and {target_mention} share a moment in a tender embrace.",
        f"{user_mention} enfolds {target_mention} in a hug that feels like coming home.",
        f"A spontaneous, joyful hug is shared between {user_mention} and {target_mention}.",
        f"{user_mention} offers {target_mention} a hug that says 'everything will be okay'.",
        f"{target_mention} melts into {user_mention}'s embrace, feeling instantly better.",
        f"{user_mention} gives {target_mention} a big, squishy hug that chases away the blues.",
        f"In {user_mention}'s hug, {target_mention} finds a moment of perfect peace.",
        f"{user_mention} and {target_mention} cling to each other in a hug that lasts for ages.",
        f"A congratulatory hug from {user_mention} makes {target_mention}'s achievement even sweeter.",
        f"{user_mention} pulls {target_mention} in for a hug, just because.",
        f"The strength of {user_mention}'s hug is a comfort to {target_mention}.",
        f"{user_mention} gives {target_mention} a gentle squeeze and a warm hug.",
        f"After a long time apart, {user_mention} and {target_mention} share an emotional reunion hug.",
        f"{user_mention} offers a supportive hug to {target_mention} during a tough time.",
        f"A playful tackle-hug from {user_mention} leaves {target_mention} laughing.",
        f"{user_mention} and {target_mention} end their day with a soft, sleepy hug.",
    ]
