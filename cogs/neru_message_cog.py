import discord
from discord.ext import commands
from discord import app_commands
import random
import logging
from .rp_messages import (
    get_sex_messages,
    get_rape_messages,
    get_kiss_messages,
    get_hug_messages,
    get_headpat_messages,
    MOLEST_MESSAGE_TEMPLATE,
    get_cumshot_messages,
)

log = logging.getLogger(__name__)


class MessageCog(commands.Cog):
    def __init__(self, bot):
        self.bot = bot

    def plural(self, word, count, suffix="s"):
        """Helper function to correctly pluralize words based on count."""
        return f"{word}{suffix if count != 1 else ''}"

    async def _ensure_usage_table_exists(self):
        """Ensure the command usage counters table exists."""
        if not hasattr(self.bot, "pg_pool") or not self.bot.pg_pool:
            log.warning("Database pool not available for usage tracking.")
            return False

        try:
            async with self.bot.pg_pool.acquire() as conn:
                await conn.execute(
                    """
                    CREATE TABLE IF NOT EXISTS command_usage_counters (
                        user1_id BIGINT NOT NULL,
                        user2_id BIGINT NOT NULL,
                        command_name TEXT NOT NULL,
                        usage_count INTEGER NOT NULL DEFAULT 1,
                        PRIMARY KEY (user1_id, user2_id, command_name)
                    )
                """
                )
            return True
        except Exception as e:
            log.error(f"Error creating usage counters table: {e}")
            return False

    async def _increment_usage_counter(
        self, user1_id: int, user2_id: int, command_name: str
    ):
        """Increment the usage counter for a command between two users."""
        if not await self._ensure_usage_table_exists():
            return

        try:
            async with self.bot.pg_pool.acquire() as conn:
                await conn.execute(
                    """
                    INSERT INTO command_usage_counters (user1_id, user2_id, command_name, usage_count)
                    VALUES ($1, $2, $3, 1)
                    ON CONFLICT (user1_id, user2_id, command_name)
                    DO UPDATE SET usage_count = command_usage_counters.usage_count + 1
                """,
                    user1_id,
                    user2_id,
                    command_name,
                )
            log.debug(
                f"Incremented usage counter for {command_name} between users {user1_id} and {user2_id}"
            )
        except Exception as e:
            log.error(f"Error incrementing usage counter: {e}")

    async def _get_usage_count(
        self, user1_id: int, user2_id: int, command_name: str
    ) -> int:
        """Get the usage count for a command between two users."""
        if not await self._ensure_usage_table_exists():
            return 0

        try:
            async with self.bot.pg_pool.acquire() as conn:
                count = await conn.fetchval(
                    """
                    SELECT usage_count FROM command_usage_counters
                    WHERE user1_id = $1 AND user2_id = $2 AND command_name = $3
                """,
                    user1_id,
                    user2_id,
                    command_name,
                )
            return count if count is not None else 0
        except Exception as e:
            log.error(f"Error getting usage count: {e}")
            return 0

    async def _get_bidirectional_usage_counts(
        self, user1_id: int, user2_id: int, command_name: str
    ) -> tuple[int, int]:
        """Get the usage counts for a command in both directions between two users.

        Returns:
            tuple[int, int]: (user1_to_user2_count, user2_to_user1_count)
        """
        if not await self._ensure_usage_table_exists():
            return 0, 0

        try:
            async with self.bot.pg_pool.acquire() as conn:
                # Get count for user1 -> user2
                count_1_to_2 = await conn.fetchval(
                    """
                    SELECT usage_count FROM command_usage_counters
                    WHERE user1_id = $1 AND user2_id = $2 AND command_name = $3
                """,
                    user1_id,
                    user2_id,
                    command_name,
                )

                # Get count for user2 -> user1
                count_2_to_1 = await conn.fetchval(
                    """
                    SELECT usage_count FROM command_usage_counters
                    WHERE user1_id = $1 AND user2_id = $2 AND command_name = $3
                """,
                    user2_id,
                    user1_id,
                    command_name,
                )

            return (
                count_1_to_2 if count_1_to_2 is not None else 0,
                count_2_to_1 if count_2_to_1 is not None else 0,
            )
        except Exception as e:
            log.error(f"Error getting bidirectional usage counts: {e}")
            return 0, 0

    # --- RP Group ---
    rp = app_commands.Group(name="rp", description="Roleplay commands")

    @rp.command(
        name="sex", description="Send a normal sex message to the mentioned user"
    )
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(member="The user to send the message to")
    async def sex_slash(self, interaction: discord.Interaction, member: discord.User):
        """Slash command version of sex."""
        # Track usage between the two users
        await self._increment_usage_counter(interaction.user.id, member.id, "neru_sex")

        # Get the bidirectional counts
        caller_to_target, target_to_caller = await self._get_bidirectional_usage_counts(
            interaction.user.id, member.id, "neru_sex"
        )

        response = random.choice(
            get_sex_messages(interaction.user.mention, member.mention)
        )
        response += f"\n-# {interaction.user.display_name} and {member.display_name} have had sex {caller_to_target} {self.plural('time', caller_to_target)}"
        if target_to_caller > 0:
            response += f", {member.display_name} and {interaction.user.display_name} have had sex {target_to_caller} {self.plural('time', target_to_caller)}"
        await interaction.response.send_message(response)

    @commands.command(name="sex")
    async def sex_legacy(self, ctx: commands.Context, member: discord.User):
        """Legacy command version of sex."""
        # Track usage between the two users
        await self._increment_usage_counter(ctx.author.id, member.id, "neru_sex")

        # Get the bidirectional counts
        caller_to_target, target_to_caller = await self._get_bidirectional_usage_counts(
            ctx.author.id, member.id, "neru_sex"
        )

        response = random.choice(get_sex_messages(ctx.author.mention, member.mention))
        response += f"\n-# {ctx.author.display_name} and {member.display_name} have had sex {caller_to_target} {self.plural('time', caller_to_target)}"
        if target_to_caller > 0:
            response += f", {member.display_name} and {ctx.author.display_name} have had sex {target_to_caller} {self.plural('time', target_to_caller)}"
        await ctx.reply(response)

    @rp.command(
        name="rape",
        description="Sends a message stating the author raped the mentioned user.",
    )
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(member="The user to mention in the message")
    async def rape_slash(self, interaction: discord.Interaction, member: discord.User):
        """Slash command version of rape."""
        # Track usage between the two users
        await self._increment_usage_counter(interaction.user.id, member.id, "neru_rape")

        # Get the bidirectional counts
        caller_to_target, target_to_caller = await self._get_bidirectional_usage_counts(
            interaction.user.id, member.id, "neru_rape"
        )

        response = random.choice(
            get_rape_messages(interaction.user.mention, member.mention)
        )
        response += f"\n-# {interaction.user.display_name} has raped {member.display_name} {caller_to_target} {self.plural('time', caller_to_target)}"
        if target_to_caller > 0:
            response += f", {member.display_name} has raped {interaction.user.display_name} {target_to_caller} {self.plural('time', target_to_caller)}"
        await interaction.response.send_message(response)

    @rp.command(
        name="kiss", description="Send a wholesome kiss message to the mentioned user"
    )
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(member="The user to send the message to")
    async def kiss_slash(self, interaction: discord.Interaction, member: discord.User):
        """Slash command version of kiss."""
        # Track usage between the two users
        await self._increment_usage_counter(interaction.user.id, member.id, "neru_kiss")

        # Get the bidirectional counts
        caller_to_target, target_to_caller = await self._get_bidirectional_usage_counts(
            interaction.user.id, member.id, "neru_kiss"
        )

        response = random.choice(
            get_kiss_messages(interaction.user.mention, member.mention)
        )
        response += f"\n-# {interaction.user.display_name} and {member.display_name} have kissed {caller_to_target} {self.plural('time', caller_to_target)}"
        if target_to_caller > 0:
            response += f", {member.display_name} and {interaction.user.display_name} have kissed {target_to_caller} {self.plural('time', target_to_caller)}"
        await interaction.response.send_message(response)

    @commands.command(name="kiss")
    async def kiss_legacy(self, ctx: commands.Context, member: discord.User):
        """Legacy command version of kiss."""
        # Track usage between the two users
        await self._increment_usage_counter(ctx.author.id, member.id, "neru_kiss")

        # Get the bidirectional counts
        caller_to_target, target_to_caller = await self._get_bidirectional_usage_counts(
            ctx.author.id, member.id, "neru_kiss"
        )

        response = random.choice(get_kiss_messages(ctx.author.mention, member.mention))
        response += f"\n-# {ctx.author.display_name} and {member.display_name} have kissed {caller_to_target} {self.plural('time', caller_to_target)}"
        if target_to_caller > 0:
            response += f", {member.display_name} and {ctx.author.display_name} have kissed {target_to_caller} {self.plural('time', target_to_caller)}"
        await ctx.reply(response)

    @rp.command(
        name="hug", description="Send a wholesome hug message to the mentioned user"
    )
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(member="The user to send the message to")
    async def hug_slash(self, interaction: discord.Interaction, member: discord.User):
        """Slash command version of hug."""
        # Track usage between the two users
        await self._increment_usage_counter(interaction.user.id, member.id, "neru_hug")

        # Get the bidirectional counts
        caller_to_target, target_to_caller = await self._get_bidirectional_usage_counts(
            interaction.user.id, member.id, "neru_hug"
        )

        response = random.choice(
            get_hug_messages(interaction.user.mention, member.mention)
        )
        response += f"\n-# {interaction.user.display_name} and {member.display_name} have hugged {caller_to_target} {self.plural('time', caller_to_target)}"
        if target_to_caller > 0:
            response += f", {member.display_name} and {interaction.user.display_name} have hugged {target_to_caller} {self.plural('time', target_to_caller)}"
        await interaction.response.send_message(response)

    @commands.command(name="hug")
    async def hug_legacy(self, ctx: commands.Context, member: discord.User):
        """Legacy command version of hug."""
        # Track usage between the two users
        await self._increment_usage_counter(ctx.author.id, member.id, "neru_hug")

        # Get the bidirectional counts
        caller_to_target, target_to_caller = await self._get_bidirectional_usage_counts(
            ctx.author.id, member.id, "neru_hug"
        )

        response = random.choice(get_hug_messages(ctx.author.mention, member.mention))
        response += f"\n-# {ctx.author.display_name} and {member.display_name} have hugged {caller_to_target} {self.plural('time', caller_to_target)}"
        if target_to_caller > 0:
            response += f", {member.display_name} and {ctx.author.display_name} have hugged {target_to_caller} {self.plural('time', target_to_caller)}"
        await ctx.reply(response)

    @rp.command(
        name="headpat",
        description="Send a wholesome headpat message to the mentioned user",
    )
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(member="The user to send the message to")
    async def headpat_slash(
        self, interaction: discord.Interaction, member: discord.User
    ):
        """Slash command version of headpat."""
        # Track usage between the two users
        await self._increment_usage_counter(
            interaction.user.id, member.id, "neru_headpat"
        )

        # Get the bidirectional counts
        caller_to_target, target_to_caller = await self._get_bidirectional_usage_counts(
            interaction.user.id, member.id, "neru_headpat"
        )

        response = random.choice(
            get_headpat_messages(interaction.user.mention, member.mention)
        )
        response += f"\n-# {interaction.user.display_name} and {member.display_name} have headpatted {caller_to_target} {self.plural('time', caller_to_target)}"
        if target_to_caller > 0:
            response += f", {member.display_name} and {interaction.user.display_name} have headpatted {target_to_caller} {self.plural('time', target_to_caller)}"
        await interaction.response.send_message(response)

    @commands.command(name="headpat")
    async def headpat_legacy(self, ctx: commands.Context, member: discord.User):
        """Legacy command version of headpat."""
        # Track usage between the two users
        await self._increment_usage_counter(ctx.author.id, member.id, "neru_headpat")

        # Get the bidirectional counts
        caller_to_target, target_to_caller = await self._get_bidirectional_usage_counts(
            ctx.author.id, member.id, "neru_headpat"
        )

        response = random.choice(
            get_headpat_messages(ctx.author.mention, member.mention)
        )
        # Get the bidirectional counts
        caller_to_target, target_to_caller = await self._get_bidirectional_usage_counts(
            ctx.author.id, member.id, "neru_headpat"
        )

        response += f"\n-# {ctx.author.display_name} and {member.display_name} have headpatted {caller_to_target} {self.plural('time', caller_to_target)}"
        if target_to_caller > 0:
            response += f", {member.display_name} and {ctx.author.display_name} have headpatted {target_to_caller} {self.plural('time', target_to_caller)}"
        await ctx.reply(response)

    @rp.command(
        name="molest", description="Send a hardcoded message to the mentioned user"
    )
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(member="The user to send the message to")
    async def molest_slash(
        self, interaction: discord.Interaction, member: discord.User
    ):
        """Slash command version of molest."""
        await self._increment_usage_counter(
            interaction.user.id, member.id, "neru_molest"
        )
        caller_to_target, target_to_caller = await self._get_bidirectional_usage_counts(
            interaction.user.id, member.id, "neru_molest"
        )

        response = MOLEST_MESSAGE_TEMPLATE.format(target=member.mention)
        response += f"\n-# {interaction.user.display_name} has molested {member.display_name} {caller_to_target} {self.plural('time', caller_to_target)}"
        if target_to_caller > 0:
            response += f", {member.display_name} has molested {interaction.user.display_name} {target_to_caller} {self.plural('time', target_to_caller)}"
        await interaction.response.send_message(response)

    @commands.command(name="molest")
    async def molest_legacy(self, ctx: commands.Context, member: discord.User):
        """Legacy command version of molest."""
        await self._increment_usage_counter(ctx.author.id, member.id, "neru_molest")
        caller_to_target, target_to_caller = await self._get_bidirectional_usage_counts(
            ctx.author.id, member.id, "neru_molest"
        )

        response = MOLEST_MESSAGE_TEMPLATE.format(target=member.mention)
        response += f"\n-# {ctx.author.display_name} has molested {member.display_name} {caller_to_target} {self.plural('time', caller_to_target)}"
        if target_to_caller > 0:
            response += f", {member.display_name} has molested {ctx.author.display_name} {target_to_caller} {self.plural('time', target_to_caller)}"
        await ctx.reply(response)

    @rp.command(
        name="cumshot", description="Send a cumshot message to the mentioned user"
    )
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(member="The user to send the message to")
    async def cumshot_slash(
        self, interaction: discord.Interaction, member: discord.User
    ):
        """Slash command version of cumshot."""
        await self._increment_usage_counter(
            interaction.user.id, member.id, "neru_cumshot"
        )
        caller_to_target, target_to_caller = await self._get_bidirectional_usage_counts(
            interaction.user.id, member.id, "neru_cumshot"
        )

        response = random.choice(
            get_cumshot_messages(interaction.user.mention, member.mention)
        )
        response += f"\n-# {interaction.user.display_name} has came on {member.display_name} {caller_to_target} {self.plural('time', caller_to_target)}"
        if target_to_caller > 0:
            response += f", {member.display_name} has came on {interaction.user.display_name} {target_to_caller} {self.plural('time', target_to_caller)}"
        await interaction.response.send_message(response)

    @commands.command(name="cumshot")
    async def cumshot_legacy(self, ctx: commands.Context, member: discord.User):
        """Legacy command version of cumshot."""
        await self._increment_usage_counter(ctx.author.id, member.id, "neru_cumshot")
        caller_to_target, target_to_caller = await self._get_bidirectional_usage_counts(
            ctx.author.id, member.id, "neru_cumshot"
        )

        response = random.choice(
            get_cumshot_messages(ctx.author.mention, member.mention)
        )
        response += f"\n-# {ctx.author.display_name} has came on {member.display_name} {caller_to_target} {self.plural('time', caller_to_target)}"
        if target_to_caller > 0:
            response += f", {member.display_name} has came on {ctx.author.display_name} {target_to_caller} {self.plural('time', target_to_caller)}"
        await ctx.reply(response)

    # --- Memes Group ---
    memes = app_commands.Group(name="memes", description="Meme and copypasta commands")

    @memes.command(
        name="seals",
        description="What the fuck did you just fucking say about me, you little bitch?",
    )
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def seals_slash(self, interaction: discord.Interaction):
        await interaction.response.send_message(
            "What the fuck did you just fucking say about me, you little bitch? I'll have you know I graduated top of my class in the Navy Seals, and I've been involved in numerous secret raids on Al-Quaeda, and I have over 300 confirmed kills. I am trained in gorilla warfare and I'm the top sniper in the entire US armed forces. You are nothing to me but just another target. I will wipe you the fuck out with precision the likes of which has never been seen before on this Earth, mark my fucking words. You think you can get away with saying that shit to me over the Internet? Think again, fucker. As we speak I am contacting my secret network of spies across the USA and your IP is being traced right now so you better prepare for the storm, maggot. The storm that wipes out the pathetic little thing you call your life. You're fucking dead, kid. I can be anywhere, anytime, and I can kill you in over seven hundred ways, and that's just with my bare hands. Not only am I extensively trained in unarmed combat, but I have access to the entire arsenal of the United States Marine Corps and I will use it to its full extent to wipe your miserable ass off the face of the continent, you little shit. If only you could have known what unholy retribution your little \"clever\" comment was about to bring down upon you, maybe you would have held your fucking tongue. But you couldn't, you didn't, and now you're paying the price, you goddamn idiot. I will shit fury all over you and you will drown in it. You're fucking dead, kiddo."
        )

    @commands.command(
        name="seals",
        help="What the fuck did you just fucking say about me, you little bitch?",
    )  # Assuming you want to keep this check for the legacy command
    async def seals_legacy(self, ctx):
        await ctx.send(
            "What the fuck did you just fucking say about me, you little bitch? I'll have you know I graduated top of my class in the Navy Seals, and I've been involved in numerous secret raids on Al-Quaeda, and I have over 300 confirmed kills. I am trained in gorilla warfare and I'm the top sniper in the entire US armed forces. You are nothing to me but just another target. I will wipe you the fuck out with precision the likes of which has never been seen before on this Earth, mark my fucking words. You think you can get away with saying that shit to me over the Internet? Think again, fucker. As we speak I am contacting my secret network of spies across the USA and your IP is being traced right now so you better prepare for the storm, maggot. The storm that wipes out the pathetic little thing you call your life. You're fucking dead, kid. I can be anywhere, anytime, and I can kill you in over seven hundred ways, and that's just with my bare hands. Not only am I extensively trained in unarmed combat, but I have access to the entire arsenal of the United States Marine Corps and I will use it to its full extent to wipe your miserable ass off the face of the continent, you little shit. If only you could have known what unholy retribution your little \"clever\" comment was about to bring down upon you, maybe you would have held your fucking tongue. But you couldn't, you didn't, and now you're paying the price, you goddamn idiot. I will shit fury all over you and you will drown in it. You're fucking dead, kiddo."
        )

    @memes.command(
        name="notlikeus",
        description="Honestly i think They Not Like Us is the only mumble rap song that is good",
    )
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def notlikeus_slash(self, interaction: discord.Interaction):
        await interaction.response.send_message(
            "Honestly i think They Not Like Us is the only mumble rap song that is good, because it calls out Drake for being a Diddy blud"
        )

    @commands.command(
        name="notlikeus",
        help="Honestly i think They Not Like Us is the only mumble rap song that is good",
    )  # Assuming you want to keep this check for the legacy command
    async def notlikeus_legacy(self, ctx):
        await ctx.send(
            "Honestly i think They Not Like Us is the only mumble rap song that is good, because it calls out Drake for being a Diddy blud"
        )

    @memes.command(name="pmo", description="icl u pmo")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    async def pmo_slash(self, interaction: discord.Interaction):
        await interaction.response.send_message(
            "icl u pmo n ts pmo sm ngl r u fr rn b fr I h8 bein diff idek anm mn js I h8 ts y r u so b so fr w me rn cz lol oms icl ts pmo sm n sb rn ngl, r u srsly srs n fr rn vro? lol atp js qt"
        )

    @commands.command(
        name="pmo",
        help="icl u pmo n ts pmo sm ngl r u fr rn b fr I h8 bein diff idek anm mn js I h8 ts y r u so b so fr w me rn cz lol oms icl ts pmo sm n sb rn ngl, r u srsly srs n fr rn vro? lol atp js qt",
    )
    async def pmo_legacy(self, ctx: commands.Context):
        await ctx.send(
            "icl u pmo n ts pmo sm ngl r u fr rn b fr I h8 bein diff idek anm mn js I h8 ts y r u so b so fr w me rn cz lol oms icl ts pmo sm n sb rn ngl, r u srsly srs n fr rn vro? lol atp js qt"
        )


async def setup(bot: commands.Bot):
    await bot.add_cog(MessageCog(bot))
