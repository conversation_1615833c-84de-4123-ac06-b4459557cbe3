import discord
from discord.ext import commands
import random
import asyncio
import time
import re
import os  # Added for file handling in error case
from typing import TYPE_CHECKING, List, Union, Dict, Any, Optional
from collections import deque  # Import deque for efficient rate limiting

# Relative imports
from .utils import format_message  # Import format_message
from .config import (
    CONTEXT_WINDOW_SIZE,  # Import context window size
    BOT_RESPONSE_RATE_LIMIT_PER_MINUTE,  # New config
    BOT_RESPONSE_RATE_LIMIT_WINDOW_SECONDS,  # New config
)

# Assuming api, utils, analysis functions are defined and imported correctly later
# We might need to adjust these imports based on final structure
# from .api import get_ai_response, get_proactive_ai_response
# from .utils import format_message, simulate_human_typing
# from .analysis import analyze_message_sentiment, update_conversation_sentiment

if TYPE_CHECKING:
    from .cog import GurtCog  # For type hinting

# Note: These listener functions need to be registered within the GurtCog class setup.
# They are defined here for separation but won't work standalone without being
# attached to the cog instance (e.g., self.bot.add_listener(on_message_listener(self), 'on_message')).


async def on_ready_listener(cog: "GurtCog"):
    """Listener function for on_ready."""
    print(f"Gurt Bot is ready! Logged in as {cog.bot.user.name} ({cog.bot.user.id})")
    print("------")

    # Now that the bot is ready, we can sync commands with Discord
    try:
        print("GurtCog: Syncing commands with Discord...")
        synced = await cog.bot.tree.sync()
        print(f"GurtCog: Synced {len(synced)} command(s)")

        # List the synced commands
        gurt_commands = [
            cmd.name
            for cmd in cog.bot.tree.get_commands()
            if cmd.name.startswith("gurt")
        ]
        print(f"GurtCog: Available Gurt commands: {', '.join(gurt_commands)}")
    except Exception as e:
        print(f"GurtCog: Failed to sync commands: {e}")
        import traceback

        traceback.print_exc()

    # --- Message history pre-loading removed ---
    # Call the initial emoji/sticker scan
    await cog.initial_emoji_sticker_scan()


async def on_message_listener(cog: "GurtCog", message: discord.Message):
    """Listener function for on_message."""
    # Import necessary functions dynamically or ensure they are passed/accessible via cog
    from .api import get_ai_response, get_proactive_ai_response
    from .utils import format_message, simulate_human_typing
    from .analysis import (
        analyze_message_sentiment,
        update_conversation_sentiment,
        identify_conversation_topics,
    )
    from .config import (
        GURT_RESPONSES,
        IGNORED_CHANNEL_IDS,
        BOT_RESPONSE_RATE_LIMIT_PER_MINUTE,
        BOT_RESPONSE_RATE_LIMIT_WINDOW_SECONDS,
    )  # Import new config

    # Don't respond to our own messages
    if message.author == cog.bot.user:
        return

    # Check if the channel is in the ignored list
    if message.channel.id in IGNORED_CHANNEL_IDS:
        # Optionally, log that a message from an ignored channel was skipped
        # print(f"Skipping message from ignored channel: {message.channel.id}")
        return

    # Don't process commands here
    if message.content.startswith(cog.bot.command_prefix):
        return

    # --- Bot Response Rate Limiting ---
    if message.author.bot:
        bot_id = message.author.id
        if not hasattr(cog, "bot_response_timestamps"):
            cog.bot_response_timestamps = {}

        if bot_id not in cog.bot_response_timestamps:
            cog.bot_response_timestamps[bot_id] = deque()

        # Clean up old timestamps
        now = time.time()
        while (
            cog.bot_response_timestamps[bot_id]
            and now - cog.bot_response_timestamps[bot_id][0]
            > BOT_RESPONSE_RATE_LIMIT_WINDOW_SECONDS
        ):
            cog.bot_response_timestamps[bot_id].popleft()

        # Check if limit is exceeded
        if (
            len(cog.bot_response_timestamps[bot_id])
            >= BOT_RESPONSE_RATE_LIMIT_PER_MINUTE
        ):
            print(
                f"Rate limit exceeded for bot {message.author.name} ({bot_id}). Skipping response."
            )
            return  # Do not respond to this bot

    # --- Cache and Track Incoming Message ---
    try:
        formatted_message = format_message(cog, message)  # Use utility function
        channel_id = message.channel.id
        user_id = message.author.id

        # --- Detect and Learn Custom Emojis/Stickers ---
        # Custom Emojis in message content
        if message.content:
            custom_emojis = re.findall(r"<(a)?:(\w+):(\d+)>", message.content)
            for (
                animated,
                name,
                emoji_id_str,
            ) in custom_emojis:  # Renamed emoji_id to emoji_id_str
                # emoji_url = f"https://cdn.discordapp.com/emojis/{emoji_id_str}.{'gif' if animated else 'png'}"
                emoji_name_key = f":{name}:"
                current_guild_id = message.guild.id if message.guild else None
                # Check if already learned, if not, add it
                existing_emoji_data = await cog.emoji_manager.get_emoji(emoji_name_key)
                if (
                    not existing_emoji_data
                    or existing_emoji_data.get("id") != emoji_id_str
                    or existing_emoji_data.get("guild_id") != current_guild_id
                ):  # Also check guild_id
                    print(
                        f"Learning custom emoji: {emoji_name_key} (ID: {emoji_id_str}, Animated: {bool(animated)}, Guild: {current_guild_id})"
                    )
                    await cog.emoji_manager.add_emoji(
                        emoji_name_key, emoji_id_str, bool(animated), current_guild_id
                    )

        # Stickers in message
        if message.stickers:
            for sticker_item in message.stickers:
                sticker_name_key = f":{sticker_item.name}:"  # Use sticker name as key
                sticker_id_str = str(sticker_item.id)  # Ensure ID is string
                current_guild_id = message.guild.id if message.guild else None
                # Check if already learned, if not, add it
                existing_sticker_data = await cog.emoji_manager.get_sticker(
                    sticker_name_key
                )
                if (
                    not existing_sticker_data
                    or existing_sticker_data.get("id") != sticker_id_str
                    or existing_sticker_data.get("guild_id") != current_guild_id
                ):  # Also check guild_id
                    print(
                        f"Learning sticker: {sticker_name_key} (ID: {sticker_id_str}, Guild: {current_guild_id})"
                    )
                    await cog.emoji_manager.add_sticker(
                        sticker_name_key, sticker_id_str, current_guild_id
                    )
        # --- End Emoji/Sticker Learning ---

        thread_id = (
            message.channel.id if isinstance(message.channel, discord.Thread) else None
        )

        # Update caches (accessing cog's state)
        # Deduplicate by message ID before appending
        def _dedup_and_append(cache_deque, msg):
            if not any(m.get("id") == msg.get("id") for m in cache_deque):
                cache_deque.append(msg)

        _dedup_and_append(
            cog.message_cache["by_channel"][channel_id], formatted_message
        )
        _dedup_and_append(cog.message_cache["by_user"][user_id], formatted_message)
        _dedup_and_append(cog.message_cache["global_recent"], formatted_message)
        if thread_id:
            _dedup_and_append(
                cog.message_cache["by_thread"][thread_id], formatted_message
            )
        if cog.bot.user.mentioned_in(message):
            _dedup_and_append(cog.message_cache["mentioned"], formatted_message)

        cog.conversation_history[channel_id].append(formatted_message)
        if thread_id:
            cog.thread_history[thread_id].append(formatted_message)

        cog.channel_activity[channel_id] = time.time()
        cog.user_conversation_mapping[user_id].add(channel_id)

        if channel_id not in cog.active_conversations:
            cog.active_conversations[channel_id] = {
                "participants": set(),
                "start_time": time.time(),
                "last_activity": time.time(),
                "topic": None,
            }
        cog.active_conversations[channel_id]["participants"].add(user_id)
        cog.active_conversations[channel_id]["last_activity"] = time.time()

        # --- Update Relationship Strengths ---
        if user_id != cog.bot.user.id:
            message_sentiment_data = analyze_message_sentiment(
                cog, message.content
            )  # Use analysis function
            sentiment_score = 0.0
            if message_sentiment_data["sentiment"] == "positive":
                sentiment_score = message_sentiment_data["intensity"] * 0.5
            elif message_sentiment_data["sentiment"] == "negative":
                sentiment_score = -message_sentiment_data["intensity"] * 0.3

            cog._update_relationship(
                str(user_id), str(cog.bot.user.id), 1.0 + sentiment_score
            )  # Access cog method

            if formatted_message.get("is_reply") and formatted_message.get(
                "replied_to_author_id"
            ):
                replied_to_id = formatted_message["replied_to_author_id"]
                if replied_to_id != str(cog.bot.user.id) and replied_to_id != str(
                    user_id
                ):
                    cog._update_relationship(
                        str(user_id), replied_to_id, 1.5 + sentiment_score
                    )

            mentioned_ids = [m["id"] for m in formatted_message.get("mentions", [])]
            for mentioned_id in mentioned_ids:
                if mentioned_id != str(cog.bot.user.id) and mentioned_id != str(
                    user_id
                ):
                    cog._update_relationship(
                        str(user_id), mentioned_id, 1.2 + sentiment_score
                    )

        # Analyze message sentiment and update conversation sentiment tracking
        if message.content:
            message_sentiment = analyze_message_sentiment(
                cog, message.content
            )  # Use analysis function
            update_conversation_sentiment(
                cog, channel_id, str(user_id), message_sentiment
            )  # Use analysis function

        # --- Add message to semantic memory ---
        if message.content and cog.memory_manager.semantic_collection:
            semantic_metadata = {
                "user_id": str(user_id),
                "user_name": message.author.name,
                "display_name": message.author.display_name,
                "channel_id": str(channel_id),
                "channel_name": getattr(message.channel, "name", "DM"),
                "guild_id": str(message.guild.id) if message.guild else None,
                "timestamp": message.created_at.timestamp(),
            }
            # Pass the entire formatted_message dictionary now
            asyncio.create_task(
                cog.memory_manager.add_message_embedding(
                    message_id=str(message.id),
                    formatted_message_data=formatted_message,
                    metadata=semantic_metadata,
                )
            )

    except Exception as e:
        print(f"Error during message caching/tracking/embedding: {e}")
    # --- End Caching & Embedding ---

    # Simple response for messages just containing "gurt"
    if message.content.lower() == "gurt":
        response = random.choice(GURT_RESPONSES)
        await message.channel.send(response)
        return

    # Check conditions for potentially responding
    bot_mentioned = cog.bot.user.mentioned_in(message)
    replied_to_bot = (
        message.reference
        and message.reference.resolved
        and message.reference.resolved.author == cog.bot.user
    )
    gurt_in_message = "gurt" in message.content.lower()

    if cog.mention_only and not (bot_mentioned or replied_to_bot or gurt_in_message):
        return

    if message.guild is None and (bot_mentioned or replied_to_bot or gurt_in_message):
        await message.channel.send("AI functionality is disabled in DMs.")
        return

    now = time.time()
    time_since_last_activity = now - cog.channel_activity.get(channel_id, 0)
    time_since_bot_spoke = now - cog.bot_last_spoke.get(channel_id, 0)

    should_consider_responding = False
    consideration_reason = "Default"
    proactive_trigger_met = False

    if bot_mentioned or replied_to_bot or gurt_in_message:
        should_consider_responding = True
        consideration_reason = "Direct mention/reply/name"
    else:
        # --- Proactive Engagement Triggers ---
        from .config import (
            PROACTIVE_LULL_THRESHOLD,
            PROACTIVE_BOT_SILENCE_THRESHOLD,
            PROACTIVE_LULL_CHANCE,
            PROACTIVE_TOPIC_RELEVANCE_THRESHOLD,
            PROACTIVE_TOPIC_CHANCE,
            PROACTIVE_RELATIONSHIP_SCORE_THRESHOLD,
            PROACTIVE_RELATIONSHIP_CHANCE,
            # Import new config values
            # Import new config values
            PROACTIVE_SENTIMENT_SHIFT_THRESHOLD,
            PROACTIVE_SENTIMENT_DURATION_THRESHOLD,
            PROACTIVE_SENTIMENT_CHANCE,
            PROACTIVE_USER_INTEREST_THRESHOLD,
            PROACTIVE_USER_INTEREST_CHANCE,
        )

        # 1. Lull Trigger
        if (
            time_since_last_activity > PROACTIVE_LULL_THRESHOLD
            and time_since_bot_spoke > PROACTIVE_BOT_SILENCE_THRESHOLD
        ):
            has_relevant_context = bool(
                cog.active_topics.get(channel_id, {}).get("topics", [])
            ) or bool(await cog.memory_manager.get_general_facts(limit=1))
            if has_relevant_context and random.random() < PROACTIVE_LULL_CHANCE:
                should_consider_responding = True
                proactive_trigger_met = True
                consideration_reason = f"Proactive: Lull ({time_since_last_activity:.0f}s idle, bot silent {time_since_bot_spoke:.0f}s)"

        # 2. Topic Relevance Trigger
        if (
            not proactive_trigger_met
            and message.content
            and cog.memory_manager.semantic_collection
        ):
            try:
                semantic_results = await cog.memory_manager.search_semantic_memory(
                    query_text=message.content, n_results=1
                )
                if semantic_results:
                    similarity_score = 1.0 - semantic_results[0].get("distance", 1.0)
                    if (
                        similarity_score >= PROACTIVE_TOPIC_RELEVANCE_THRESHOLD
                        and time_since_bot_spoke > 120
                    ):
                        if random.random() < PROACTIVE_TOPIC_CHANCE:
                            should_consider_responding = True
                            proactive_trigger_met = True
                            consideration_reason = f"Proactive: Relevant topic (Sim: {similarity_score:.2f})"
                            print(
                                f"Topic relevance trigger met for msg {message.id}. Sim: {similarity_score:.2f}"
                            )
                    else:
                        print(
                            f"Topic relevance trigger skipped by chance ({PROACTIVE_TOPIC_CHANCE}). Sim: {similarity_score:.2f}"
                        )
            except Exception as semantic_e:
                print(f"Error during semantic search for topic trigger: {semantic_e}")

        # 3. Relationship Score Trigger
        if not proactive_trigger_met:
            try:
                user_id_str = str(message.author.id)
                bot_id_str = str(cog.bot.user.id)
                key_1, key_2 = (
                    (user_id_str, bot_id_str)
                    if user_id_str < bot_id_str
                    else (bot_id_str, user_id_str)
                )
                relationship_score = cog.user_relationships.get(key_1, {}).get(
                    key_2, 0.0
                )
                if (
                    relationship_score >= PROACTIVE_RELATIONSHIP_SCORE_THRESHOLD
                    and time_since_bot_spoke > 60
                ):
                    if random.random() < PROACTIVE_RELATIONSHIP_CHANCE:
                        should_consider_responding = True
                        proactive_trigger_met = True
                        consideration_reason = (
                            f"Proactive: High relationship ({relationship_score:.1f})"
                        )
                        print(
                            f"Relationship trigger met for user {user_id_str}. Score: {relationship_score:.1f}"
                        )
                    else:
                        print(
                            f"Relationship trigger skipped by chance ({PROACTIVE_RELATIONSHIP_CHANCE}). Score: {relationship_score:.1f}"
                        )
            except Exception as rel_e:
                print(f"Error during relationship trigger check: {rel_e}")

        # 4. Sentiment Shift Trigger
        if not proactive_trigger_met:
            channel_sentiment_data = cog.conversation_sentiment.get(channel_id, {})
            overall_sentiment = channel_sentiment_data.get("overall", "neutral")
            sentiment_intensity = channel_sentiment_data.get("intensity", 0.5)
            sentiment_last_update = channel_sentiment_data.get(
                "last_update", 0
            )  # Need last update time
            sentiment_duration = (
                now - sentiment_last_update
            )  # How long has this sentiment been dominant?

            if (
                overall_sentiment != "neutral"
                and sentiment_intensity >= PROACTIVE_SENTIMENT_SHIFT_THRESHOLD
                and sentiment_duration >= PROACTIVE_SENTIMENT_DURATION_THRESHOLD
                and time_since_bot_spoke > 180
            ):  # Bot hasn't spoken recently about this
                if random.random() < PROACTIVE_SENTIMENT_CHANCE:
                    should_consider_responding = True
                    proactive_trigger_met = True
                    consideration_reason = f"Proactive: Sentiment Shift ({overall_sentiment}, Intensity: {sentiment_intensity:.2f}, Duration: {sentiment_duration:.0f}s)"
                    print(
                        f"Sentiment Shift trigger met for channel {channel_id}. Sentiment: {overall_sentiment}, Intensity: {sentiment_intensity:.2f}, Duration: {sentiment_duration:.0f}s"
                    )
                else:
                    print(
                        f"Sentiment Shift trigger skipped by chance ({PROACTIVE_SENTIMENT_CHANCE}). Sentiment: {overall_sentiment}"
                    )

        # 5. User Interest Trigger (Based on Gurt's interests mentioned in message)
        if not proactive_trigger_met and message.content:
            try:
                gurt_interests = await cog.memory_manager.get_interests(
                    limit=10, min_level=PROACTIVE_USER_INTEREST_THRESHOLD
                )
                if gurt_interests:
                    message_content_lower = message.content.lower()
                    mentioned_interest = None
                    for interest_topic, interest_level in gurt_interests:
                        # Simple check if interest topic is in message
                        if re.search(
                            r"\b" + re.escape(interest_topic.lower()) + r"\b",
                            message_content_lower,
                        ):
                            mentioned_interest = interest_topic
                            break  # Found a mentioned interest

                    if (
                        mentioned_interest and time_since_bot_spoke > 90
                    ):  # Bot hasn't spoken recently
                        if random.random() < PROACTIVE_USER_INTEREST_CHANCE:
                            should_consider_responding = True
                            proactive_trigger_met = True
                            consideration_reason = f"Proactive: Gurt Interest Mentioned ('{mentioned_interest}')"
                            print(
                                f"Gurt Interest trigger met for message {message.id}. Interest: '{mentioned_interest}'"
                            )
                        else:
                            print(
                                f"Gurt Interest trigger skipped by chance ({PROACTIVE_USER_INTEREST_CHANCE}). Interest: '{mentioned_interest}'"
                            )
            except Exception as interest_e:
                print(f"Error during Gurt Interest trigger check: {interest_e}")

        # 6. Active Goal Relevance Trigger
        if not proactive_trigger_met and message.content:
            try:
                # Fetch 1-2 active goals with highest priority
                active_goals = await cog.memory_manager.get_goals(
                    status="active", limit=2
                )
                if active_goals:
                    message_content_lower = message.content.lower()
                    relevant_goal = None
                    for goal in active_goals:
                        # Simple check: does message content relate to goal description?
                        # TODO: Improve this check, maybe use semantic similarity or keyword extraction from goal details
                        goal_keywords = set(
                            re.findall(
                                r"\b\w{3,}\b", goal.get("description", "").lower()
                            )
                        )  # Basic keywords from description
                        message_words = set(
                            re.findall(r"\b\w{3,}\b", message_content_lower)
                        )
                        if (
                            len(goal_keywords.intersection(message_words)) > 1
                        ):  # Require >1 keyword overlap
                            relevant_goal = goal
                            break

                    if (
                        relevant_goal and time_since_bot_spoke > 120
                    ):  # Bot hasn't spoken recently
                        # Use a slightly higher chance for goal-related triggers?
                        goal_relevance_chance = (
                            PROACTIVE_USER_INTEREST_CHANCE * 1.2
                        )  # Example: Reuse interest chance slightly boosted
                        if random.random() < goal_relevance_chance:
                            should_consider_responding = True
                            proactive_trigger_met = True
                            goal_desc_short = relevant_goal.get("description", "N/A")[
                                :40
                            ]
                            consideration_reason = f"Proactive: Relevant Active Goal ('{goal_desc_short}...')"
                            print(
                                f"Active Goal trigger met for message {message.id}. Goal ID: {relevant_goal.get('goal_id')}"
                            )
                        else:
                            print(
                                f"Active Goal trigger skipped by chance ({goal_relevance_chance:.2f})."
                            )
            except Exception as goal_trigger_e:
                print(f"Error during Active Goal trigger check: {goal_trigger_e}")

        # --- Fallback Contextual Chance ---
        if not should_consider_responding:  # Check if already decided to respond
            # Fetch current personality traits for chattiness
            persistent_traits = await cog.memory_manager.get_all_personality_traits()
            chattiness = persistent_traits.get(
                "chattiness", 0.7
            )  # Use default if fetch fails

            base_chance = chattiness * 0.5
            activity_bonus = 0
            if time_since_last_activity > 120:
                activity_bonus += 0.1
            if time_since_bot_spoke > 300:
                activity_bonus += 0.1
            topic_bonus = 0
            active_channel_topics = cog.active_topics.get(channel_id, {}).get(
                "topics", []
            )
            if message.content and active_channel_topics:
                topic_keywords = set(t["topic"].lower() for t in active_channel_topics)
                message_words = set(re.findall(r"\b\w+\b", message.content.lower()))
                if topic_keywords.intersection(message_words):
                    topic_bonus += 0.15
            sentiment_modifier = 0
            channel_sentiment_data = cog.conversation_sentiment.get(channel_id, {})
            overall_sentiment = channel_sentiment_data.get("overall", "neutral")
            sentiment_intensity = channel_sentiment_data.get("intensity", 0.5)
            if overall_sentiment == "negative" and sentiment_intensity > 0.6:
                sentiment_modifier = -0.1

            final_chance = min(
                max(
                    base_chance + activity_bonus + topic_bonus + sentiment_modifier,
                    0.05,
                ),
                0.8,
            )
            if random.random() < final_chance:
                should_consider_responding = True
                consideration_reason = f"Contextual chance ({final_chance:.2f})"
            else:
                consideration_reason = f"Skipped (chance {final_chance:.2f})"

    print(
        f"Consideration check for message {message.id}: {should_consider_responding} (Reason: {consideration_reason})"
    )

    if not should_consider_responding:
        return

    # --- Call AI and Handle Response ---
    cog.current_channel = (
        message.channel
    )  # Ensure current channel is set for API calls/tools

    try:  # This is the outer try block
        response_dict: Dict[str, Any]
        sticker_ids_to_send: List[str] = []  # Initialize sticker_ids_to_send

        if proactive_trigger_met:
            print(
                f"Calling get_proactive_ai_response for message {message.id} due to: {consideration_reason}"
            )
            response_dict, sticker_ids_to_send = await get_proactive_ai_response(
                cog, message, consideration_reason
            )
        else:
            print(f"Calling get_ai_response for message {message.id}")
            response_dict, sticker_ids_to_send = await get_ai_response(cog, message)

        # --- Handle AI Response Bundle ---
        # The 'initial_response' key is no longer used as get_ai_response/get_proactive_ai_response
        # now directly return the final processed response data in the first element of the tuple.
        # We'll use 'final_response_data' to hold this.
        final_response_data = response_dict.get("final_response")
        error_msg = response_dict.get("error")
        fallback_initial = response_dict.get(
            "fallback_initial"
        )  # This might still be relevant for critical errors

        if error_msg:
            print(f"Critical Error from AI response function: {error_msg}")
            error_notification = f"Oops! Something went wrong while processing that. (`{error_msg[:100]}`)"
            try:
                # await message.channel.send(error_notification) # Error notification disabled
                print("disabled error notification")
            except Exception as send_err:
                print(f"Failed to send error notification to channel: {send_err}")
            return

        # --- Process and Send Responses ---
        sent_any_message = False
        reacted = False

        # Helper function to handle sending a single response text and caching
        async def send_response_content(
            response_data_param: Optional[Dict[str, Any]],  # Renamed to avoid conflict
            response_label: str,
            original_message: discord.Message,
            current_sticker_ids: List[
                str
            ],  # Pass the specific sticker IDs for this response
        ) -> bool:
            nonlocal sent_any_message
            if (
                not response_data_param
                or not isinstance(response_data_param, dict)
                or not response_data_param.get("should_respond")
                or not response_data_param.get("content")
            ):
                # If content is None but stickers are present, we might still want to send
                if not (
                    response_data_param
                    and response_data_param.get("should_respond")
                    and current_sticker_ids
                ):
                    return False

            response_text = response_data_param.get(
                "content", ""
            )  # Default to empty string if content is None
            reply_to_id = response_data_param.get("reply_to_message_id")
            message_reference = None

            print(f"Preparing to send {response_label} content...")

            if reply_to_id and isinstance(reply_to_id, str) and reply_to_id.isdigit():
                try:
                    original_reply_msg = await original_message.channel.fetch_message(
                        int(reply_to_id)
                    )
                    if original_reply_msg:
                        message_reference = original_reply_msg.to_reference(
                            fail_if_not_exists=False
                        )
                        print(f"Will reply to message ID: {reply_to_id}")
                    else:
                        print(
                            f"Warning: Could not fetch message {reply_to_id} to reply to."
                        )
                except (ValueError, discord.NotFound, discord.Forbidden) as fetch_err:
                    print(
                        f"Warning: Error fetching message {reply_to_id} to reply to: {fetch_err}"
                    )
                except Exception as e:
                    print(f"Unexpected error fetching reply message {reply_to_id}: {e}")
            elif reply_to_id:
                print(f"Warning: Invalid reply_to_id format received: {reply_to_id}")

            ping_matches = re.findall(r"\[PING:\s*([^\]]+)\s*\]", response_text)
            if ping_matches:
                print(f"Found ping placeholders: {ping_matches}")
                from .tools import get_user_id

                for user_name_to_ping in ping_matches:
                    user_id_result = await get_user_id(cog, user_name_to_ping.strip())
                    if user_id_result and user_id_result.get("status") == "success":
                        user_id_to_ping = user_id_result.get("user_id")
                        if user_id_to_ping:
                            response_text = response_text.replace(
                                f"[PING: {user_name_to_ping}]",
                                f"<@{user_id_to_ping}>",
                                1,
                            )
                            print(
                                f"Replaced ping placeholder for '{user_name_to_ping}' with <@{user_id_to_ping}>"
                            )
                        else:
                            print(
                                f"Warning: get_user_id succeeded for '{user_name_to_ping}' but returned no ID."
                            )
                            response_text = response_text.replace(
                                f"[PING: {user_name_to_ping}]", user_name_to_ping, 1
                            )
                    else:
                        print(
                            f"Warning: Could not find user ID for ping placeholder '{user_name_to_ping}'. Error: {user_id_result.get('error')}"
                        )
                        response_text = response_text.replace(
                            f"[PING: {user_name_to_ping}]", user_name_to_ping, 1
                        )

            discord_stickers_to_send = (
                [
                    discord.Object(id=int(s_id))
                    for s_id in current_sticker_ids
                    if s_id.isdigit()
                ]
                if current_sticker_ids
                else []
            )

            # Only proceed if there's text or stickers to send
            if not response_text and not discord_stickers_to_send:
                if response_data_param and response_data_param.get(
                    "should_respond"
                ):  # Log if it was supposed to respond but had nothing
                    print(
                        f"Warning: {response_label} response marked 'should_respond' but has no content or stickers."
                    )
                return False

            if (
                len(response_text) > 1900
            ):  # Discord character limit is 2000, 1900 gives buffer
                filepath = f"gurt_{response_label}_{original_message.id}.txt"
                try:
                    with open(filepath, "w", encoding="utf-8") as f:
                        f.write(response_text)
                    await original_message.channel.send(
                        f"{response_label.capitalize()} response too long:",
                        file=discord.File(filepath),
                        reference=message_reference,
                        mention_author=True,
                        stickers=discord_stickers_to_send,
                    )
                    sent_any_message = True
                    print(
                        f"Sent {response_label} content as file (Reply: {bool(message_reference)}, Stickers: {len(discord_stickers_to_send)})."
                    )
                    return True
                except Exception as file_e:
                    print(
                        f"Error writing/sending long {response_label} response file: {file_e}"
                    )
                finally:
                    try:
                        os.remove(filepath)
                    except OSError as os_e:
                        print(f"Error removing temp file {filepath}: {os_e}")
            else:
                try:
                    # Only enter typing context if there's text to send
                    if response_text:
                        async with original_message.channel.typing():
                            await simulate_human_typing(
                                cog, original_message.channel, response_text
                            )

                    sent_msg = await original_message.channel.send(
                        (
                            response_text if response_text else None
                        ),  # Send None if only stickers
                        reference=message_reference,
                        mention_author=True,
                        stickers=discord_stickers_to_send,
                    )
                    sent_any_message = True
                    bot_response_cache_entry = format_message(cog, sent_msg)
                    cog.message_cache["by_channel"][channel_id].append(
                        bot_response_cache_entry
                    )
                    cog.message_cache["global_recent"].append(bot_response_cache_entry)
                    cog.bot_last_spoke[channel_id] = time.time()
                    identified_topics = identify_conversation_topics(
                        cog, [bot_response_cache_entry]
                    )
                    if identified_topics:
                        topic = identified_topics[0]["topic"].lower().strip()
                        cog.gurt_participation_topics[topic] += 1
                        print(
                            f"Tracked Gurt participation ({response_label}) in topic: '{topic}'"
                        )
                    print(
                        f"Sent {response_label} content (Reply: {bool(message_reference)}, Stickers: {len(discord_stickers_to_send)})."
                    )

                    # --- Record Gurt's response to a bot for rate limiting ---
                    if original_message.author.bot:
                        bot_id = original_message.author.id
                        # Ensure the deque exists (it should from the check at the start of on_message_listener)
                        if bot_id not in cog.bot_response_timestamps:
                            cog.bot_response_timestamps[bot_id] = deque()
                        cog.bot_response_timestamps[bot_id].append(time.time())
                        print(
                            f"Recorded Gurt's response to bot {original_message.author.name} ({bot_id}). Current count: {len(cog.bot_response_timestamps[bot_id])}"
                        )

                    return True
                except Exception as send_e:
                    print(f"Error sending {response_label} content: {send_e}")
            return False

        # Send the main response content (which is now in final_response_data)
        # sticker_ids_to_send is already defined from the AI response unpacking
        sent_main_message = await send_response_content(
            final_response_data, "final", message, sticker_ids_to_send
        )

        # Handle Reaction (using final_response_data)
        if final_response_data and isinstance(final_response_data, dict):
            emoji_to_react = final_response_data.get("react_with_emoji")
            if emoji_to_react and isinstance(emoji_to_react, str):
                try:
                    if 1 <= len(emoji_to_react) <= 4 and not re.match(
                        r"<a?:.+?:\d+>", emoji_to_react
                    ):
                        if not sent_any_message:  # Only react if no message was sent
                            await message.add_reaction(emoji_to_react)
                            reacted = True
                            print(
                                f"Bot reacted to message {message.id} with {emoji_to_react}"
                            )
                        else:
                            print(
                                f"Skipping reaction {emoji_to_react} because a message was already sent."
                            )
                    else:
                        print(f"Invalid emoji format: {emoji_to_react}")
                except Exception as e:
                    print(f"Error adding reaction '{emoji_to_react}': {e}")

        # Log if response was intended but nothing was sent/reacted
        intended_action = final_response_data and final_response_data.get(
            "should_respond"
        )
        action_taken = sent_main_message or reacted
        if intended_action and not action_taken:
            print(
                f"Warning: AI response intended action but nothing sent/reacted. Response data: {final_response_data}"
            )

        # Handle fallback if no other action was taken and fallback_initial is present
        if (
            not action_taken
            and fallback_initial
            and fallback_initial.get("should_respond")
        ):
            print("Attempting to send fallback_initial response...")
            await send_response_content(
                fallback_initial, "fallback", message, []
            )  # No stickers for fallback

    except Exception as e:
        print(f"Exception in on_message listener main block: {str(e)}")
        import traceback

        traceback.print_exc()
        if bot_mentioned or replied_to_bot:
            await message.channel.send(
                random.choice(["...", "*confused gurting*", "brain broke sorry"])
            )


@commands.Cog.listener()
async def on_reaction_add_listener(
    cog: "GurtCog",
    reaction: discord.Reaction,
    user: Union[discord.Member, discord.User],
):
    """Listener function for on_reaction_add."""
    # Import necessary config/functions if not globally available
    from .config import EMOJI_SENTIMENT
    from .analysis import identify_conversation_topics

    if user.bot or reaction.message.author.id != cog.bot.user.id:
        return

    message_id = str(reaction.message.id)
    emoji_str = str(reaction.emoji)
    sentiment = "neutral"
    if emoji_str in EMOJI_SENTIMENT["positive"]:
        sentiment = "positive"
    elif emoji_str in EMOJI_SENTIMENT["negative"]:
        sentiment = "negative"

    if sentiment == "positive":
        cog.gurt_message_reactions[message_id]["positive"] += 1
    elif sentiment == "negative":
        cog.gurt_message_reactions[message_id]["negative"] += 1
    cog.gurt_message_reactions[message_id]["timestamp"] = time.time()

    if not cog.gurt_message_reactions[message_id].get("topic"):
        try:
            gurt_msg_data = next(
                (
                    msg
                    for msg in cog.message_cache["global_recent"]
                    if msg["id"] == message_id
                ),
                None,
            )
            if gurt_msg_data and gurt_msg_data["content"]:
                identified_topics = identify_conversation_topics(
                    cog, [gurt_msg_data]
                )  # Pass cog
                if identified_topics:
                    topic = identified_topics[0]["topic"].lower().strip()
                    cog.gurt_message_reactions[message_id]["topic"] = topic
                    print(
                        f"Reaction added to Gurt msg ({message_id}) on topic '{topic}'. Sentiment: {sentiment}"
                    )
                else:
                    print(f"Reaction added to Gurt msg ({message_id}), topic unknown.")
            else:
                print(f"Reaction added, but Gurt msg {message_id} not in cache.")
        except Exception as e:
            print(f"Error determining topic for reaction on msg {message_id}: {e}")
    else:
        print(
            f"Reaction added to Gurt msg ({message_id}) on known topic '{cog.gurt_message_reactions[message_id]['topic']}'. Sentiment: {sentiment}"
        )


@commands.Cog.listener()
async def on_reaction_remove_listener(
    cog: "GurtCog",
    reaction: discord.Reaction,
    user: Union[discord.Member, discord.User],
):
    """Listener function for on_reaction_remove."""
    from .config import EMOJI_SENTIMENT  # Import necessary config

    if user.bot or reaction.message.author.id != cog.bot.user.id:
        return

    message_id = str(reaction.message.id)
    emoji_str = str(reaction.emoji)
    sentiment = "neutral"
    if emoji_str in EMOJI_SENTIMENT["positive"]:
        sentiment = "positive"
    elif emoji_str in EMOJI_SENTIMENT["negative"]:
        sentiment = "negative"

    if message_id in cog.gurt_message_reactions:
        if sentiment == "positive":
            cog.gurt_message_reactions[message_id]["positive"] = max(
                0, cog.gurt_message_reactions[message_id]["positive"] - 1
            )
        elif sentiment == "negative":
            cog.gurt_message_reactions[message_id]["negative"] = max(
                0, cog.gurt_message_reactions[message_id]["negative"] - 1
            )
        print(f"Reaction removed from Gurt msg ({message_id}). Sentiment: {sentiment}")


# --- New Listener Functions for Guild Asset Updates ---


async def on_guild_join_listener(cog: "GurtCog", guild: discord.Guild):
    """Listener function for on_guild_join."""
    print(f"Gurt joined a new guild: {guild.name} ({guild.id})")
    print(f"Processing emojis and stickers for new guild: {guild.name}")
    # Schedule the processing as a background task to avoid blocking
    asyncio.create_task(cog._fetch_and_process_guild_assets(guild))


async def on_guild_emojis_update_listener(
    cog: "GurtCog",
    guild: discord.Guild,
    before: List[discord.Emoji],
    after: List[discord.Emoji],
):
    """Listener function for on_guild_emojis_update."""
    print(
        f"Emojis updated in guild: {guild.name} ({guild.id}). Before: {len(before)}, After: {len(after)}"
    )

    before_map = {emoji.id: emoji for emoji in before}
    after_map = {emoji.id: emoji for emoji in after}

    tasks = []

    # Process added emojis
    for emoji_id, emoji_obj in after_map.items():
        if emoji_id not in before_map:
            print(
                f"New emoji added: {emoji_obj.name} ({emoji_id}) in guild {guild.name}"
            )
            tasks.append(asyncio.create_task(cog._process_single_emoji(emoji_obj)))
        else:
            # Check for changes in existing emojis (e.g., name change)
            # The _process_single_emoji method already checks if a description exists and is valid.
            # If the name changes, the old key won't match, so it will be treated as new by the manager if name is key.
            # If ID is the primary key for checking existence, then a name change might need explicit handling.
            # Current EmojiManager uses name as key, so a name change means old is gone, new is added.
            # If an emoji's URL or other relevant properties change, _process_single_emoji will handle it.
            before_emoji = before_map[emoji_id]
            if before_emoji.name != emoji_obj.name or str(before_emoji.url) != str(
                emoji_obj.url
            ):
                print(
                    f"Emoji changed: {before_emoji.name} -> {emoji_obj.name} or URL changed in guild {guild.name}"
                )
                # Remove old entry if name changed, as EmojiManager uses name as key
                if before_emoji.name != emoji_obj.name:
                    await cog.emoji_manager.remove_emoji(f":{before_emoji.name}:")
                tasks.append(asyncio.create_task(cog._process_single_emoji(emoji_obj)))

    # Process removed emojis
    for emoji_id, emoji_obj in before_map.items():
        if emoji_id not in after_map:
            print(
                f"Emoji removed: {emoji_obj.name} ({emoji_id}) from guild {guild.name}"
            )
            await cog.emoji_manager.remove_emoji(
                f":{emoji_obj.name}:"
            )  # Remove by name key

    if tasks:
        print(f"Queued {len(tasks)} tasks for emoji updates in guild {guild.name}")
        await asyncio.gather(*tasks, return_exceptions=True)
    else:
        print(
            f"No new or significantly changed emojis to process in guild {guild.name}"
        )


async def on_guild_stickers_update_listener(
    cog: "GurtCog",
    guild: discord.Guild,
    before: List[discord.StickerItem],
    after: List[discord.StickerItem],
):
    """Listener function for on_guild_stickers_update."""
    print(
        f"Stickers updated in guild: {guild.name} ({guild.id}). Before: {len(before)}, After: {len(after)}"
    )

    before_map = {sticker.id: sticker for sticker in before}
    after_map = {sticker.id: sticker for sticker in after}
    tasks = []

    # Process added or changed stickers
    for sticker_id, sticker_obj in after_map.items():
        if sticker_id not in before_map:
            print(
                f"New sticker added: {sticker_obj.name} ({sticker_id}) in guild {guild.name}"
            )
            tasks.append(asyncio.create_task(cog._process_single_sticker(sticker_obj)))
        else:
            before_sticker = before_map[sticker_id]
            # Check for relevant changes (name, URL, format)
            if (
                before_sticker.name != sticker_obj.name
                or str(before_sticker.url) != str(sticker_obj.url)
                or before_sticker.format != sticker_obj.format
            ):
                print(
                    f"Sticker changed: {before_sticker.name} -> {sticker_obj.name} or URL/format changed in guild {guild.name}"
                )
                if before_sticker.name != sticker_obj.name:
                    await cog.emoji_manager.remove_sticker(f":{before_sticker.name}:")
                tasks.append(
                    asyncio.create_task(cog._process_single_sticker(sticker_obj))
                )

    # Process removed stickers
    for sticker_id, sticker_obj in before_map.items():
        if sticker_id not in after_map:
            print(
                f"Sticker removed: {sticker_obj.name} ({sticker_id}) from guild {guild.name}"
            )
            await cog.emoji_manager.remove_sticker(f":{sticker_obj.name}:")

    if tasks:
        print(f"Queued {len(tasks)} tasks for sticker updates in guild {guild.name}")
        await asyncio.gather(*tasks, return_exceptions=True)
    else:
        print(
            f"No new or significantly changed stickers to process in guild {guild.name}"
        )


async def on_voice_transcription_received_listener(
    cog: "GurtCog", guild: discord.Guild, user: discord.Member, text: str
):
    """Listener for transcribed voice messages."""
    from .api import get_ai_response  # For processing the text
    from .utils import (
        format_message,
        simulate_human_typing,
    )  # For creating pseudo-message and sending response
    from .config import (
        IGNORED_CHANNEL_IDS,
        VOICE_DEDICATED_TEXT_CHANNEL_ENABLED,
        VOICE_LOG_SPEECH_TO_DEDICATED_CHANNEL,
    )  # Import new config

    print(
        f"Voice transcription received from {user.name} ({user.id}) in {guild.name}: '{text}'"
    )

    # Avoid processing if user is a bot (including GURT itself if its speech gets transcribed)
    if user.bot:
        print(f"Skipping voice transcription from bot user: {user.name}")
        return

    # Determine a relevant text channel for context and potential text responses.
    # This is a simplification; a more robust solution might track last active text channel per user/guild.
    # For now, try to use a "general" or the first available text channel in the guild.
    # Or, if GURT is in a voice channel, it might have an associated text channel.
    # This part needs careful consideration for the best UX.

    text_channel = None
    if VOICE_DEDICATED_TEXT_CHANNEL_ENABLED:
        voice_gateway_cog = cog.bot.get_cog("VoiceGatewayCog")
        if voice_gateway_cog:
            text_channel = voice_gateway_cog.get_dedicated_text_channel_for_guild(
                guild.id
            )
            if text_channel:
                print(
                    f"Using dedicated voice text channel: {text_channel.name} ({text_channel.id})"
                )
            else:
                print(
                    f"Dedicated voice text channel feature is ON, but no channel found for guild {guild.id}. Aborting voice transcription processing."
                )
                return  # Do not proceed if dedicated channel is expected but not found
        else:
            print(
                "VoiceGatewayCog not found. Cannot get dedicated text channel. Aborting voice transcription processing."
            )
            return
    else:  # Fallback to old behavior if dedicated channel feature is off
        if guild:
            if (
                guild.system_channel
                and guild.system_channel.permissions_for(guild.me).send_messages
            ):
                text_channel = guild.system_channel
            else:
                for channel in guild.text_channels:
                    if (
                        channel.name.lower()
                        in ["general", "chat", "lounge", "discussion"]
                        and channel.permissions_for(guild.me).send_messages
                    ):
                        text_channel = channel
                        break
                if not text_channel and guild.text_channels:
                    text_channel = guild.text_channels[0]

    if not text_channel:
        print(
            f"Could not find a suitable text channel in guild {guild.name} for voice transcription context. Aborting."
        )
        return

    # Check if this pseudo-channel context should be ignored (applies to both dedicated and fallback)
    if text_channel.id in IGNORED_CHANNEL_IDS:
        print(
            f"Skipping voice transcription as target context channel {text_channel.name} ({text_channel.id}) is ignored."
        )
        return

    # Construct a pseudo-message object or dictionary
    # This needs to be compatible with what get_ai_response and format_message expect.
    # We'll create a dictionary similar to what format_message would produce.

    # Create a mock discord.Message object for format_message and get_ai_response
    # This is a bit hacky but helps reuse existing logic.
    class PseudoMessage:
        def __init__(self, author, content, channel, guild_obj, created_at, id_val):
            self.author = author
            self.content = content
            self.channel = channel
            self.guild = guild_obj
            self.created_at = created_at
            self.id = id_val  # Needs a unique ID, timestamp can work
            self.reference = None  # No reply context for voice
            self.attachments = []
            self.embeds = []
            self.stickers = []
            self.reactions = []
            self.mentions = []  # Could parse mentions from text if needed
            self.mention_everyone = "@everyone" in content
            self.role_mentions = []  # Could parse role mentions
            self.channel_mentions = []  # Could parse channel mentions
            self.flags = discord.MessageFlags._from_value(0)  # Default flags
            self.type = discord.MessageType.default
            self.pinned = False
            self.tts = False
            self.system_content = ""
            self.activity = None
            self.application = None
            self.components = []
            self.interaction = None
            self.webhook_id = None
            self.jump_url = f"https://discord.com/channels/{guild.id}/{channel.id}/{id_val}"  # Approximate

        def to_reference(
            self, fail_if_not_exists: bool = True
        ):  # Add fail_if_not_exists
            return discord.MessageReference(
                message_id=self.id,
                channel_id=self.channel.id,
                guild_id=self.guild.id,
                fail_if_not_exists=fail_if_not_exists,
            )

    pseudo_msg_id = int(time.time() * 1000000)  # Create a somewhat unique ID
    pseudo_message_obj = PseudoMessage(
        author=user,
        content=text,
        channel=text_channel,  # Use the determined text channel for context
        guild_obj=guild,
        created_at=discord.utils.utcnow(),
        id_val=pseudo_msg_id,
    )

    # Update cog's current_channel for the context of this interaction
    original_current_channel = cog.current_channel
    cog.current_channel = text_channel

    # --- Cache the transcribed voice message as if it were a text message ---
    try:
        formatted_pseudo_message = format_message(
            cog, pseudo_message_obj
        )  # Use utility function
        # Ensure channel_id and user_id are correctly sourced from the pseudo_message_obj or its components
        msg_channel_id = pseudo_message_obj.channel.id
        msg_user_id = (
            pseudo_message_obj.author.id
        )  # This is a discord.User/Member object

        # Deduplicate by message ID before appending (using helper from on_message_listener)
        # Note: _dedup_and_append might need to be accessible here or its logic replicated.
        # For simplicity, direct append, assuming pseudo_msg_id is unique enough for this context.
        # If _dedup_and_append is not directly usable, simple append is a starting point.
        # Consider making _dedup_and_append a static method or utility if widely needed.

        # Helper for deduplication (copied from on_message_listener for now)
        def _dedup_and_append_local(cache_deque, msg_dict_to_add):
            if not any(m.get("id") == msg_dict_to_add.get("id") for m in cache_deque):
                cache_deque.append(msg_dict_to_add)

        _dedup_and_append_local(
            cog.message_cache["by_channel"].setdefault(
                msg_channel_id, deque(maxlen=CONTEXT_WINDOW_SIZE)
            ),
            formatted_pseudo_message,
        )
        _dedup_and_append_local(
            cog.message_cache["by_user"].setdefault(
                msg_user_id, deque(maxlen=CONTEXT_WINDOW_SIZE * 2)
            ),
            formatted_pseudo_message,
        )  # User cache might be larger
        _dedup_and_append_local(
            cog.message_cache["global_recent"], formatted_pseudo_message
        )
        # No thread_id for pseudo_message currently
        # No mention check for pseudo_message currently

        cog.conversation_history.setdefault(
            msg_channel_id, deque(maxlen=CONTEXT_WINDOW_SIZE)
        ).append(formatted_pseudo_message)

        cog.channel_activity[msg_channel_id] = time.time()  # Update activity timestamp
        cog.user_conversation_mapping.setdefault(msg_user_id, set()).add(msg_channel_id)

        if msg_channel_id not in cog.active_conversations:
            cog.active_conversations[msg_channel_id] = {
                "participants": set(),
                "start_time": time.time(),
                "last_activity": time.time(),
                "topic": None,
            }
        cog.active_conversations[msg_channel_id]["participants"].add(msg_user_id)
        cog.active_conversations[msg_channel_id]["last_activity"] = time.time()

        print(
            f"Cached voice transcription from {user.name} into history of channel {text_channel.name} ({msg_channel_id})."
        )

        # --- Add message to semantic memory (if applicable) ---
        if (
            text and cog.memory_manager.semantic_collection
        ):  # Check if 'text' (original transcription) is not empty
            semantic_metadata = {
                "user_id": str(msg_user_id),
                "user_name": user.name,
                "display_name": user.display_name,
                "channel_id": str(msg_channel_id),
                "channel_name": getattr(text_channel, "name", "VoiceContext"),
                "guild_id": str(guild.id) if guild else None,
                "timestamp": pseudo_message_obj.created_at.timestamp(),
                "is_voice_transcription": True,  # Add a flag
            }
            asyncio.create_task(
                cog.memory_manager.add_message_embedding(
                    message_id=str(pseudo_message_obj.id),
                    formatted_message_data=formatted_pseudo_message,
                    metadata=semantic_metadata,
                )
            )
            print(
                f"Scheduled voice transcription from {user.name} for semantic embedding."
            )

    except Exception as e:
        print(f"Error during voice transcription caching/embedding: {e}")
        import traceback

        traceback.print_exc()
    # --- End Caching & Embedding ---

    try:
        # Process the transcribed text as if it were a regular message
        # The get_ai_response function will handle tool calls, including speak_in_voice_channel
        print(
            f"Processing transcribed text from {user.name} via get_ai_response: '{text}'"
        )
        response_dict, sticker_ids_to_send = await get_ai_response(
            cog, pseudo_message_obj
        )

        final_response_data = response_dict.get("final_response")
        error_msg = response_dict.get("error")

        if error_msg:
            print(f"Error from AI processing voice transcription: {error_msg}")
            # Decide if GURT should say something about the error in voice
            # For now, just log it.
            return

        if final_response_data and final_response_data.get("should_respond"):
            response_text = final_response_data.get("content", "")

            # If GURT is in a voice channel in this guild, it might have already decided to speak
            # via a tool call within get_ai_response (if speak_in_voice_channel was called).
            # If not, and there's text, we could make it speak here as a fallback,
            # but it's better if the AI decides to use the speak_in_voice_channel tool.

            if response_text:
                # Force speak the response if it's from a voice transcription context
                speak_tool_func = cog.TOOL_MAPPING.get("speak_in_voice_channel")
                if speak_tool_func:
                    print(
                        f"Forcing voice response for transcription: '{response_text[:50]}...'"
                    )
                    speak_result = await speak_tool_func(
                        cog, text_to_speak=response_text
                    )

                    if speak_result.get("status") == "success":
                        print(
                            f"Successfully forced voice response. Text log handled by speak_in_voice_channel tool if enabled."
                        )
                        # The speak_in_voice_channel tool will log to the dedicated text channel
                        # if VOICE_LOG_SPEECH_TO_DEDICATED_CHANNEL is true.
                        # No need to send separately from here if that config is true.
                        # If VOICE_LOG_SPEECH_TO_DEDICATED_CHANNEL is false, no text log of GURT's speech will appear.
                    else:
                        print(
                            f"Forced speak_in_voice_channel failed: {speak_result.get('error')}"
                        )
                        # Fallback: if speaking failed, send it as text to the dedicated channel
                        # so the user at least gets a response.
                        try:
                            fallback_msg = await text_channel.send(
                                f"(Voice output failed) GURT: {response_text}"
                            )
                            print(
                                f"Sent fallback text response to {text_channel.name} for voice transcription failure."
                            )
                            # Cache this fallback text response
                            bot_response_cache_entry = format_message(cog, fallback_msg)
                            cog.message_cache["by_channel"][text_channel.id].append(
                                bot_response_cache_entry
                            )
                            cog.message_cache["global_recent"].append(
                                bot_response_cache_entry
                            )
                            cog.bot_last_spoke[text_channel.id] = time.time()
                        except Exception as send_fallback_err:
                            print(
                                f"Error sending fallback text for voice failure: {send_fallback_err}"
                            )
                else:
                    print(
                        "speak_in_voice_channel tool not found. Sending text response as fallback."
                    )
                    try:
                        # Fallback to text if tool is missing
                        fallback_msg = await text_channel.send(
                            f"(Voice tool missing) GURT: {response_text}"
                        )
                        print(
                            f"Sent fallback text response to {text_channel.name} due to missing voice tool."
                        )
                        # Cache this fallback text response
                        bot_response_cache_entry = format_message(cog, fallback_msg)
                        cog.message_cache["by_channel"][text_channel.id].append(
                            bot_response_cache_entry
                        )
                        cog.message_cache["global_recent"].append(
                            bot_response_cache_entry
                        )
                        cog.bot_last_spoke[text_channel.id] = time.time()
                    except Exception as send_fallback_err3:
                        print(
                            f"Error sending fallback text for missing voice tool: {send_fallback_err3}"
                        )

            # Handle reactions if any (similar to on_message)
            emoji_to_react = final_response_data.get("react_with_emoji")
            if emoji_to_react and isinstance(emoji_to_react, str):
                # React to the pseudo_message or a real message if one was sent?
                # For simplicity, let's assume reaction isn't the primary mode for voice.
                print(
                    f"Voice transcription AI suggested reaction: {emoji_to_react} (currently not implemented for voice-originated interactions)"
                )

    except Exception as e:
        print(f"Error in on_voice_transcription_received_listener: {e}")
        import traceback

        traceback.print_exc()
    finally:
        cog.current_channel = (
            original_current_channel  # Restore original current_channel
        )


async def on_voice_state_update_listener(
    cog: "GurtCog",
    member: discord.Member,
    before: discord.VoiceState,
    after: discord.VoiceState,
):
    """Listener for voice state updates (e.g., user joining/leaving VC)."""
    from .config import IGNORED_CHANNEL_IDS  # To respect ignored channels if applicable

    # We need access to tools, so we'd call them via cog.bot.get_cog("Gurt").tool_name or similar
    # For now, let's assume tools are called through a helper or directly if GurtCog has them.
    # This listener might trigger GURT to use join_voice_channel or leave_voice_channel tools.

    if member.bot:  # Ignore bots, including GURT itself
        return

    guild = member.guild
    gurt_vc = guild.voice_client if guild else None

    # Scenario 1: User joins a voice channel
    if not before.channel and after.channel:
        print(
            f"User {member.name} joined voice channel {after.channel.name} in guild {guild.name}"
        )

        # Conditions for GURT to consider auto-joining:
        # 1. GURT is not already in a voice channel in this guild OR is in the same channel.
        # 2. The user who joined is someone GURT is actively interacting with or has high relationship.
        # 3. The target voice channel is not an ignored context.

        if (
            after.channel.id in IGNORED_CHANNEL_IDS
        ):  # Or some other form of channel permission check
            print(
                f"GURT will not auto-join {after.channel.name} as it's an ignored/restricted context."
            )
            return

        # Check if GURT should consider joining this user
        # Simple check: is user in recent conversation participants?
        is_interacting_user = False
        if guild.id in cog.active_conversations:
            if member.id in cog.active_conversations[guild.id]["participants"]:
                is_interacting_user = True

        # More advanced: check relationship score
        # relationship_score = cog.user_relationships.get(str(min(member.id, cog.bot.user.id)), {}).get(str(max(member.id, cog.bot.user.id)), 0.0)
        # if relationship_score > SOME_THRESHOLD: is_interacting_user = True

        if not is_interacting_user:
            print(
                f"User {member.name} joined VC, but GURT is not actively interacting with them. No auto-join."
            )
            return

        # If GURT is already in a VC in this guild but it's a *different* channel
        if gurt_vc and gurt_vc.is_connected() and gurt_vc.channel != after.channel:
            print(
                f"GURT is already in {gurt_vc.channel.name}. Not auto-joining {member.name} in {after.channel.name} for now."
            )
            # Future: Could ask LLM if it should move.
            return

        # If GURT is not in a VC in this guild, or is in the same one (but not listening perhaps)
        if (
            not gurt_vc
            or not gurt_vc.is_connected()
            or gurt_vc.channel != after.channel
        ):
            print(
                f"GURT considering auto-joining {member.name} in {after.channel.name}."
            )
            # Here, GURT's "brain" (LLM or simpler logic) would decide.
            # For simplicity, let's make it auto-join if the above conditions are met.
            # This would use the `join_voice_channel` tool.
            # The tool itself is async and defined in gurt/tools.py

            # To call a tool, we'd typically go through the AI's tool-using mechanism.
            # For an autonomous action, GURT's core logic would invoke the tool.
            # This listener is part of that core logic.

            # We need the GurtCog instance to call its methods or access tools.
            # The `cog` parameter *is* the GurtCog instance.
            gurt_tool_cog = cog  # The GurtCog instance itself

            if (
                hasattr(gurt_tool_cog, "TOOL_MAPPING")
                and "join_voice_channel" in gurt_tool_cog.TOOL_MAPPING
            ):
                join_tool_func = gurt_tool_cog.TOOL_MAPPING["join_voice_channel"]
                print(
                    f"Attempting to auto-join VC {after.channel.id} for user {member.name}"
                )
                try:
                    # The tool function expects `cog` as its first arg, then params.
                    # We pass `gurt_tool_cog` (which is `self` if this were a cog method)
                    # and then the arguments for the tool.
                    tool_result = await join_tool_func(
                        gurt_tool_cog, channel_id=str(after.channel.id)
                    )
                    if tool_result.get("status") == "success":
                        print(
                            f"GURT successfully auto-joined {member.name} in {after.channel.name}."
                        )
                        # Optionally, GURT could say "Hey [user], I'm here!"
                        if "speak_in_voice_channel" in gurt_tool_cog.TOOL_MAPPING:
                            speak_tool_func = gurt_tool_cog.TOOL_MAPPING[
                                "speak_in_voice_channel"
                            ]
                            await speak_tool_func(
                                gurt_tool_cog,
                                text_to_speak=f"Hey {member.display_name}, I saw you joined so I came too!",
                            )
                    else:
                        print(f"GURT auto-join failed: {tool_result.get('error')}")
                except Exception as e:
                    print(f"Error during GURT auto-join attempt: {e}")
            else:
                print("join_voice_channel tool not found in GURT's TOOL_MAPPING.")

    # Scenario 2: User leaves a voice channel GURT is in
    elif before.channel and not after.channel:
        # User disconnected from all VCs or was moved out by admin
        print(
            f"User {member.name} left voice channel {before.channel.name} in guild {guild.name}"
        )
        if gurt_vc and gurt_vc.is_connected() and gurt_vc.channel == before.channel:
            # Check if GURT is now alone in the channel
            if (
                len(gurt_vc.channel.members) == 1
                and gurt_vc.channel.members[0] == guild.me
            ):
                print(f"GURT is now alone in {gurt_vc.channel.name}. Auto-leaving.")
                gurt_tool_cog = cog
                if (
                    hasattr(gurt_tool_cog, "TOOL_MAPPING")
                    and "leave_voice_channel" in gurt_tool_cog.TOOL_MAPPING
                ):
                    leave_tool_func = gurt_tool_cog.TOOL_MAPPING["leave_voice_channel"]
                    try:
                        tool_result = await leave_tool_func(gurt_tool_cog)
                        if tool_result.get("status") == "success":
                            print(f"GURT successfully auto-left {before.channel.name}.")
                        else:
                            print(f"GURT auto-leave failed: {tool_result.get('error')}")
                    except Exception as e:
                        print(f"Error during GURT auto-leave attempt: {e}")
                else:
                    print("leave_voice_channel tool not found in GURT's TOOL_MAPPING.")

    # Scenario 3: User moves between voice channels
    elif before.channel and after.channel and before.channel != after.channel:
        print(
            f"User {member.name} moved from {before.channel.name} to {after.channel.name} in guild {guild.name}"
        )
        # If GURT was in the `before.channel` with the user, and is now alone, it might leave.
        if gurt_vc and gurt_vc.is_connected() and gurt_vc.channel == before.channel:
            if (
                len(gurt_vc.channel.members) == 1
                and gurt_vc.channel.members[0] == guild.me
            ):
                print(
                    f"GURT is now alone in {before.channel.name} after {member.name} moved. Auto-leaving."
                )
                # (Same auto-leave logic as above)
                gurt_tool_cog = cog
                if (
                    hasattr(gurt_tool_cog, "TOOL_MAPPING")
                    and "leave_voice_channel" in gurt_tool_cog.TOOL_MAPPING
                ):
                    leave_tool_func = gurt_tool_cog.TOOL_MAPPING["leave_voice_channel"]
                    await leave_tool_func(gurt_tool_cog)  # Fire and forget for now

        # If GURT is not in a VC, or was not in the user's new VC, and user is interacting, consider joining `after.channel`
        # This logic is similar to Scenario 1.
        if after.channel.id not in IGNORED_CHANNEL_IDS:
            is_interacting_user = False
            if guild.id in cog.active_conversations:
                if member.id in cog.active_conversations[guild.id]["participants"]:
                    is_interacting_user = True

            if is_interacting_user:
                if (
                    not gurt_vc
                    or not gurt_vc.is_connected()
                    or gurt_vc.channel != after.channel
                ):
                    print(
                        f"GURT considering auto-joining {member.name} in their new channel {after.channel.name}."
                    )
                    gurt_tool_cog = cog
                    if (
                        hasattr(gurt_tool_cog, "TOOL_MAPPING")
                        and "join_voice_channel" in gurt_tool_cog.TOOL_MAPPING
                    ):
                        join_tool_func = gurt_tool_cog.TOOL_MAPPING[
                            "join_voice_channel"
                        ]
                        try:
                            tool_result = await join_tool_func(
                                gurt_tool_cog, channel_id=str(after.channel.id)
                            )
                            if tool_result.get("status") == "success":
                                print(
                                    f"GURT successfully auto-joined {member.name} in {after.channel.name} after they moved."
                                )
                                if (
                                    "speak_in_voice_channel"
                                    in gurt_tool_cog.TOOL_MAPPING
                                ):
                                    speak_tool_func = gurt_tool_cog.TOOL_MAPPING[
                                        "speak_in_voice_channel"
                                    ]
                                    await speak_tool_func(
                                        gurt_tool_cog,
                                        text_to_speak=f"Found you, {member.display_name}!",
                                    )
                            else:
                                print(
                                    f"GURT auto-join (move) failed: {tool_result.get('error')}"
                                )
                        except Exception as e:
                            print(f"Error during GURT auto-join (move) attempt: {e}")
                    else:
                        print("join_voice_channel tool not found for auto-join (move).")
