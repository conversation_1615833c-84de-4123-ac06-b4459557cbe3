# Info For AI Agents

- All cogs from the cogs directory are automatically loaded by default. (In the main bot, at least. <PERSON><PERSON><PERSON> and <PERSON><PERSON> have hard-coded lists, but these bots are irrelevant unless specifically mentioned.)
- If you need to exclude a cog from being loaded, check `commands.py`.
- Hybrid commands are preferred to only legacy prefix commands unless specifically requested.
- Pyte<PERSON> (`pytest -q`) won’t work in this environment, so don’t run it.
- For testing you can use `pylint` and `python -m py_compile`.
- You are using Python 3.11.
- All pip dependencies are installed in the environment (except packages related to locally running AI, due to their large size).
- **When you’ve finished and tested your task, create a fresh `pr/<desc>` branch from your work branch, push *that* to the remote, and open a pull request on <PERSON><PERSON><PERSON> using the `tea` CLI. Never push your work branch or `master` directly.**
- The `tea` cli will likely require the argument `--repo slipstream/discordbot`
- The base branch for your pull request should be `master`
- If you hit hard-to-fix errors while pushing or during the PR, leave them for the user to handle after you’ve made reasonable attempts (e.g., resolving merge conflicts or fetching missing remote work).
- There are no nested `AGENTS.md` files; this is the only one in the project.
- You have a Python script `tavilytool.py` in the project root that you can use to search the web.


# Tavily API Script Usage Instructions

## Basic Usage
Search for information using simple queries:
```bash
python tavilytool.py "your search query"
```

## Examples
```bash
python tavilytool.py "latest AI development 2024"
python tavilytool.py "how to make chocolate chip cookies"
python tavilytool.py "current weather in New York"
python tavilytool.py "best programming practices Python"
```

## Advanced Options

### Search Depth
- **Basic search**: `python tavilytool.py "query"` (default)
- **Advanced search**: `python tavilytool.py "query" --depth advanced`

### Control Results
- **Limit results**: `python tavilytool.py "query" --max-results 3`
- **Include images**: `python tavilytool.py "query" --include-images`
- **Skip AI answer**: `python tavilytool.py "query" --no-answer`

### Domain Filtering
- **Include specific domains**: `python tavilytool.py "query" --include-domains reddit.com stackoverflow.com`
- **Exclude domains**: `python tavilytool.py "query" --exclude-domains wikipedia.org`

### Output Format
- **Formatted output**: `python tavilytool.py "query"` (default - human readable)
- **Raw JSON**: `python tavilytool.py "query" --raw` (for programmatic processing)

## Output Structure
The default formatted output includes:
- 🤖 **AI Answer**: Direct answer to your query
- 🔍 **Search Results**: Titles, URLs, and content snippets
- 🖼️ **Images**: Relevant images (when `--include-images` is used)

## Command Combinations
```bash
# Advanced search with images, limited results
python tavilytool.py "machine learning tutorials" --depth advanced --include-images --max-results 3

# Search specific sites only, raw output
python tavilytool.py "Python best practices" --include-domains github.com stackoverflow.com --raw

# Quick search without AI answer
python tavilytool.py "today's news" --no-answer --max-results 5
```

## Tips
- Always quote your search queries to handle spaces and special characters
- Use `--max-results` to control response length and API usage
- Use `--raw` when you need to parse results programmatically
- Combine options as needed for specific use cases