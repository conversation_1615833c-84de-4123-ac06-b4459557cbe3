param (
    [string]$FilePath
)

# --- Add this block for error handling ---
if ([string]::IsNullOrEmpty($FilePath)) {
    Write-Error "Error: No file path provided."
    Write-Host "Usage: .\blamepercent.ps1 -FilePath <path_to_file>"
    Write-Host "Example: .\blamepercent.ps1 -FilePath '.\your_script.ps1'"
    exit 1
}
# --- End of added block ---

if (-not (Test-Path $FilePath)) {
    Write-Error "File '$FilePath' does not exist."
    exit 1
}

# Set console output to UTF-8
[Console]::OutputEncoding = [Text.UTF8Encoding]::new()

# Run git blame on the file, get author names per line
$blameOutput = git blame --line-porcelain $FilePath | Where-Object { $_ -like 'author *' } | ForEach-Object { ($_ -replace 'author ', '').Trim() }

if (-not $blameOutput) {
    Write-Error "No git blame info found. Make sure this file is tracked by git."
    exit 1
}

# Function to sanitize author names, removing problematic characters
function Clean-AuthorName {
    param($name)
    # Remove control characters and non-printable characters
    $clean = -join ($name.ToCharArray() | Where-Object { 
        $c = [int][char]$_
        ($c -ge 32 -and $c -le 126) -or ($c -ge 160) # keep printable ASCII and extended UTF chars
    })
    return $clean
}

# Count lines per author
$authorCounts = @{}
$totalLines = 0

foreach ($author in $blameOutput) {
    $cleanAuthor = Clean-AuthorName $author
    if ($authorCounts.ContainsKey($cleanAuthor)) {
        $authorCounts[$cleanAuthor] += 1
    } else {
        $authorCounts[$cleanAuthor] = 1
    }
    $totalLines++
}

# Calculate and display percentages
Write-Host "Code contribution percentages for file: $FilePath"
foreach ($author in $authorCounts.Keys) {
    $percentage = [math]::Round(($authorCounts[$author] / $totalLines) * 100, 2)
    Write-Host "$author : $percentage %"
}