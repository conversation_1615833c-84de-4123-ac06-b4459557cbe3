import os
import sys
import asyncio
import argparse
import neru_bot

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run the Neru Discord Bot.")
    parser.add_argument(
        "--disable-ai", action="store_true", help="Disable AI functionality"
    )
    args = parser.parse_args()

    try:
        # Pass the arguments to the main function
        asyncio.run(neru_bot.main(args))
    except KeyboardInterrupt:
        print("Neru Bot stopped by user.")
    except Exception as e:
        print(f"An error occurred running Neru Bot: {e}")
